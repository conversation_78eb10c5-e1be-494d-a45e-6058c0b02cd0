<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.youzan.cloud</groupId>
		<artifactId>waimaitong-agent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>waimaitong-agent-biz</artifactId>
	<name>waimaitong-agent-biz</name>

	<dependencies>
		<dependency>
			<groupId>com.youzan.cloud</groupId>
			<artifactId>waimaitong-agent-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.youzan.cloud</groupId>
			<artifactId>waimaitong-agent-dal</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.youzan.boot</groupId>
			<artifactId>youzan-api-rpc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.youzan</groupId>
			<artifactId>cloud-base-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.youzan</groupId>
			<artifactId>cloud-base-extension-point</artifactId>
		</dependency>
		<dependency>
			<groupId>com.youzan</groupId>
			<artifactId>cloud-base-message</artifactId>
		</dependency>
		<dependency>
			<groupId>com.youzan</groupId>
			<artifactId>cloud-base-bifrost-open-sdk</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
			<version>2.6.1</version>
		</dependency>
		<dependency>
			<groupId>com.youzan.cloud</groupId>
			<artifactId>extension-point-api</artifactId>
		</dependency>
	</dependencies>
</project>
