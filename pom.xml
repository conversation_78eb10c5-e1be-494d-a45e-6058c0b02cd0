<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.youzan</groupId>
		<artifactId>cloud-parent</artifactId>
		<version>1.1.3-RELEASE</version>
		<relativePath/>
	</parent>

	<groupId>com.youzan.cloud</groupId>
	<artifactId>waimaitong-agent</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<name>waimaitong-agent</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<extension-point-api.version>1.11.3.22</extension-point-api.version>
		<youzan-api-rpc.version>2.0.2-RELEASE</youzan-api-rpc.version>
	</properties>

	<modules>
		<module>waimaitong-agent-api</module>
		<module>waimaitong-agent-deploy</module>
		<module>waimaitong-agent-biz</module>
		<module>waimaitong-agent-dal</module>
		<module>waimaitong-agent-web</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.youzan.cloud</groupId>
				<artifactId>extension-point-api</artifactId>
				<version>${extension-point-api.version}</version>
			</dependency>
			<dependency>
				<groupId>com.youzan.boot</groupId>
				<artifactId>youzan-api-rpc</artifactId>
				<version>${youzan-api-rpc.version}</version>
			</dependency>
			<dependency>
				<artifactId>okio</artifactId>
				<groupId>com.squareup.okio</groupId>
				<version>1.13.0</version>
			</dependency>
			<dependency>
				<groupId>com.youzan</groupId>
				<artifactId>cloud-base-bifrost-open-sdk</artifactId>
				<version>1.0.1-RELEASE</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
</project>
