{"name": "react-pages", "version": "0.1.0", "description": "有赞定制react前端页面", "author": "<EMAIL>", "private": true, "scripts": {"dev": "node webpack/dev.js", "build": "node webpack/build.js"}, "dependencies": {"react": "^16.7.0", "zent": "^6.5.2", "react-dom": "^16.7.0", "react-router-dom": "^4.3.1", "react-dev-utils": "^7.0.0"}, "devDependencies": {"@babel/core": "7.4.3", "@babel/plugin-transform-react-jsx-source": "^7.2.0", "autoprefixer": "^7.1.2", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^8.2.1", "babel-loader": "^7.1.1", "babel-plugin-import": "^1.11.0", "babel-plugin-zent": "^1.2.2", "babel-preset-react-app": "^9.0.0", "chalk": "^2.0.1", "concurrently": "^4.1.0", "css-loader": "^0.28.0", "eslint": "^4.15.0", "eslint-config-react-app": "^3.0.6", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-flowtype": "2.50.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-jsx-a11y": "6.1.2", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-react": "7.11.1", "eslint-plugin-standard": "^3.0.1", "express": "^4.17.1", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "fs-extra": "^8.1.0", "glob": "^7.1.3", "html-webpack-plugin": "^2.30.1", "http-proxy-middleware": "^0.19.1", "node-notifier": "^5.1.2", "node-sass": "^4.12.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "precss": "^4.0.0", "raw-loader": "^3.0.0", "rimraf": "^2.6.0", "sass-loader": "7.1.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "style-loader": "0.23.0", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-merge": "^4.1.0", "youzanyun": "^0.1.13"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "homepage": "/react/", "babel": {"presets": ["react-app"], "plugins": ["zent"]}, "eslintConfig": {"extends": "react-app"}}