import React from 'react';
import { BlockLoading, Notify } from 'zent';
import { Stage } from './constant';
import Intro from './intro';
import List from './list';
import Progress from './progress';

class Page extends React.Component {
  state = {
    stage: null,
    agentId: null,
  };

  componentDidMount() {
    const { agentId, page } = this.yz.getPageQuery();

    // TODO 上线时可以删除
    if (page) {
      this.setState({ agentId: +agentId, stage: page });
      return;
    }

    this.setState({ agentId: +agentId }, this.fetchAgentDetail);
  }

  fetchAgentDetail = () => {
    const { agentId } = this.state;

    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/querySkills',
      useBizRequest: true,
      method: 'get',
      data: {
        agentId,
      },
    })
      .then((res) => {
        const skills = res.data.data ?? [];
        if (!skills || skills.length === 0) {
          Notify.error('技能数据有误，请重试');
          return;
        }
        if (skills.every(({ initialized }) => !initialized)) {
          this.setState({ stage: Stage.Intro });
          return;
        }
        this.setState({ stage: Stage.List });
      })
      .catch(() => {
        this.setState({ stage: Stage.Intro });
      });
  };

  handleChangeStage = (stage) => {
    this.setState({ stage });
  };

  render() {
    const { stage, agentId } = this.state;

    if (!agentId) {
      return <BlockLoading loading />;
    }

    switch (stage) {
      case Stage.Intro:
        return (
          <Intro
            yz={this.yz}
            agentId={agentId}
            onChangeStage={this.handleChangeStage}
          />
        );
      case Stage.Progress:
        return (
          <Progress
            yz={this.yz}
            agentId={agentId}
            onChangeStage={this.handleChangeStage}
          />
        );
      case Stage.List:
        return (
          <List
            yz={this.yz}
            agentId={agentId}
            onChangeStage={this.handleChangeStage}
          />
        );
      default:
        return null;
    }
  }
}

export default Page;
