createPage({
  config: {
    navigationBarTitleText: '全网外卖通托管',
  },
  created() {
    console.log('intro created');
  },
  // 生命周期回调 —— 在页面挂载之前执行，建议发起网络请求
  beforeMount() {},
  // 生命周期回调 —— 在页面挂载时执行
  mounted() {
    yz.setNavigationBarTitle('全网外卖通托管');
  },
  // 生命周期回调 —— 在页面卸载时执行，建议进行事件和定时器的资源回收
  destroyed() {},
  // 开发者可以添加任意方法到 methods 中，使用 this 来调用
  methods: {},
  // 页面渲染函数，该函数需要返回一个 Tee 组件
  render: (h) => h(page),
});
