export const OperatorType = {
  /** 小于 */
  LessThan: 1,
  /** 小于等于 */
  LessThanOrEqual: 2,
  /** 等于 */
  Equals: 3,
  /** 大于 */
  GreaterThan: 4,
  /** 大于等于 */
  GreaterThanOrEqual: 5,
  /** 包含 */
  In: 7,
};

export const OperatorText = {
  [OperatorType.Equals]: '=',
  [OperatorType.GreaterThan]: '>',
  [OperatorType.GreaterThanOrEqual]: '>=',
  [OperatorType.LessThan]: '<',
  [OperatorType.LessThanOrEqual]: '<=',
  [OperatorType.In]: '包含',
};

export const SkillTemplateIdMap = {
  /** 店铺设置 */
  ShopSetting: -1,
  /** 商品售罄 */
  GoodsSoldout: 41,
  /** 商品售罄提醒 */
  GoodsSoldoutReminder: 42,
  /** 商品淘汰 */
  GoodsDiscard: 43,
  /** 商品下架 */
  GoodsOffShelf: 44,
  /** 商品解绑 */
  GoodsUnbind: 45,
  /** 订单就餐 */
  OrderDiningout: 46,
  /** 订单售后 */
  OrderAftersale: 47,
  /** 订单超时 */
  OrderTimeout: 48,
  /** 订单取餐状态 */
  OrderPickupStatus: 49,
};
