import React from "react";
import { Notify } from "zent";
import ChannelRunStatus from "./components/channel-run-status";
import SkillList from "./components/skill-list";
import { SkillTemplateIdMap } from "./constants";
import { TestShopIds } from "../constant";
import "./index.scss";

class Page extends React.Component {
  state = {
    overviewDatas: [],
    channelDatas: [],
    agentDetail: {
      status: 0,
      updateTime: 0,
      isShowStatus: false,
    },
    enableSkillsNum: 0,
  };

  itemSelectorRef = null;

  async componentDidMount() {
    const { agentId, yz: yzProps } = this.props;
    const { kdtId } = yzProps.data.shop;
    const isTestShop = TestShopIds.includes(kdtId);

    this.fetchOverviewDatas();
    this.fetchChannelDatas();
    this.yz
      .queryComponentById("waimaitong-agent-overview")
      .then((agentOverview) => {
        // 获取技能列表
        agentOverview.beforeSkillGet(() => {
          return Promise.all([
            this.queryWaimaiShopDefaultAIConfig(),
            yz.request({
              url: "/v4/third-plugin/waimaitong-agent/querySkills",
              useBizRequest: true,
              method: "get",
              data: {
                agentId,
              },
            }),
          ])
            .then(([waimaiShopDefaultAIConfig, skillsRes]) => {
              const shopConfig = waimaiShopDefaultAIConfig.data.data;
              const skills = skillsRes.data.data ?? [];
              const enumGroups = [
                [SkillTemplateIdMap.ShopSetting],
                [
                  SkillTemplateIdMap.GoodsSoldout,
                  SkillTemplateIdMap.GoodsSoldoutReminder,
                  SkillTemplateIdMap.GoodsDiscard,
                  SkillTemplateIdMap.GoodsOffShelf,
                  SkillTemplateIdMap.GoodsUnbind,
                ],
                [
                  !isTestShop && SkillTemplateIdMap.OrderDiningout,
                  SkillTemplateIdMap.OrderAftersale,
                  SkillTemplateIdMap.OrderTimeout,
                  SkillTemplateIdMap.OrderPickupStatus,
                ].filter(Boolean),
              ];

              const skillGroups = [
                {
                  list: [],
                },
                {
                  list: [],
                  header: "商品托管",
                },
                {
                  list: [],
                  header: "订单托管",
                },
              ];

              const allSkills = [
                shopConfig
                  ? {
                      skillTemplateId: SkillTemplateIdMap.ShopSetting,
                      name: shopConfig.name,
                      description: shopConfig.description,
                      enable: shopConfig.enable,
                    }
                  : {},
                ...skills,
              ];

              allSkills.forEach((item) => {
                const { skillTemplateId } = item;
                enumGroups.forEach((enumGroup, index) => {
                  if (enumGroup.includes(skillTemplateId)) {
                    skillGroups[index].list.push(item);
                  }
                });
              });

              const { enableSkillsNum } = allSkills.reduce(
                (acc, { enable }) => {
                  if (enable) {
                    acc.enableSkillsNum += 1;
                  }
                  return acc;
                },
                { enableSkillsNum: 0 }
              );
              this.setState({
                enableSkillsNum,
              });

              return {
                list: skillGroups,
              };
            })
            .catch((error) => {
              console.log("skill-error", error);
              return {
                list: [1],
              };
            });
        });

        // 获取执行记录
        agentOverview.beforeLogsGet((payload) => {
          const { page, pageSize } = payload;
          return yz
            .request({
              url: "/v4/third-plugin/waimaitong-agent/pageExecuteRecords",
              useBizRequest: true,
              method: "get",
              data: {
                myAgentId: agentId,
                page,
                pageSize,
              },
            })
            .then((res) => {
              const { data, totalCount, page, pageSize } = res.data.data;
              return {
                list: data.map(
                  ({
                    executeTime,
                    executeStatus,
                    executeContent,
                    executeResult,
                  }) => ({
                    executeTime,
                    executeStatus,
                    executeContent,
                    executeResult,
                  })
                ),
                totalCount,
                page,
                pageSize,
              };
            });
        });
      })
      .catch((err) => {
        console.log("err", err);
      });

    this.yz
      .queryComponentById("waimaitong-item-selector")
      .then((itemSelector) => {
        this.itemSelectorRef = itemSelector;
      });
  }

  async fetchOverviewDatas() {
    const { agentId } = this.props;

    try {
      const res = await yz.request({
        url: "/v4/third-plugin/waimaitong-agent/getMyAgentDetail",
        useBizRequest: true,
        method: "get",
        data: {
          myAgentId: agentId,
        },
      });

      const { dataItems, agentInfo } = res.data.data;
      this.setState({
        agentDetail: {
          status: agentInfo.status,
          updateTime: new Date(agentInfo.updateTime).getTime(),
          isShowStatus: true,
        },
      });

      const { createTime } = agentInfo;

      const tradeOverview = await yz.request({
        url: "/v4/third-plugin/waimaitong-agent/getWmAgentOverview",
        useBizRequest: true,
        method: "get",
        data: {
          startDate: new Date(createTime).getTime(),
          endDate: Date.now(),
          agentId,
          skillTemplateId: SkillTemplateIdMap.OrderDiningout,
        },
      });

      const tradeData = tradeOverview.data.data;

      this.setState({
        overviewDatas: [
          ...dataItems.map(({ title, content }) => ({
            label: title,
            value: content,
          })),
          ...tradeData.map(({ title, content }) => ({
            label: title,
            value: content,
          })),
          {
            label: "已开启技能点",
            value: this.state.enableSkillsNum,
          },
        ],
      });
    } catch (error) {
      Notify.error("获取数据失败");
      console.log("err", error);
    }
  }

  queryWaimaiShopDefaultAIConfig() {
    return yz
      .request({
        url: "/v4/third-plugin/waimaitong-agent/queryWmShopHostConfig",
        useBizRequest: true,
        method: "get",
      })
      .catch((err) => {
        console.log("err", err);
      });
  }

  fetchChannelDatas() {
    const { agentId } = this.props;
    yz.request({
      url: "/v4/third-plugin/waimaitong-agent/queryWmChannelHostRunStatus",
      useBizRequest: true,
      method: "get",
      data: {
        agentId,
      },
    })
      .then((res) => {
        this.setState({
          channelDatas: res.data?.data ?? [],
        });
      })
      .catch((err) => {
        console.log("err", err);
      });
  }

  refreshSkill = () => {
    this.yz
      .queryComponentById("waimaitong-agent-overview")
      .then((agentOverview) => {
        agentOverview.refreshSkill();
      })
      .catch((err) => {
        console.log("refresh-error", err);
      });
  };

  handleEnableSkill = (enable) => {
    const newEnableSkillsNum = enable
      ? this.state.enableSkillsNum + 1
      : this.state.enableSkillsNum - 1;
    this.setState((prevState) => ({
      enableSkillsNum: newEnableSkillsNum,
      overviewDatas: prevState.overviewDatas.map(({ label, value }) =>
        label.includes("开启技能点")
          ? { label, value: newEnableSkillsNum }
          : { label, value }
      ),
    }));
  };

  render() {
    const { overviewDatas, channelDatas, agentDetail } = this.state;
    const { agentId } = this.props;

    return (
      <div className="list-wrapper">
        <div className="container">
          <div className="content">
            <AgentOverview
              cloudId="waimaitong-agent-overview"
              logo="https://img01.yzcdn.cn/upload_files/2025/03/19/FkEEDUNOGG3Jqbi_ZchdgohqY7yD.png"
              name="全网外卖通托管"
              overviewDatas={overviewDatas}
              status={agentDetail.status}
              updateTime={agentDetail.updateTime}
              isShowStatus={false}
            >
              <div slot="desc">
                <ChannelRunStatus channelDatas={channelDatas} yz={yz} />
              </div>
              <div slot="skill">
                {({ skill: { header, list } }, index) => {
                  return (
                    <SkillList
                      key={index}
                      header={header}
                      skills={list}
                      yz={yz}
                      refreshSkill={this.refreshSkill}
                      handleEnableSkill={this.handleEnableSkill}
                      itemSelectorRef={this.itemSelectorRef}
                      agentId={agentId}
                    />
                  );
                }}
              </div>
            </AgentOverview>
          </div>
        </div>
        <ItemSelectorDialog
          cloudId="waimaitong-item-selector"
          shopType={7}
          itemSelectorConfigList={[
            {
              type: "item",
              configList: [
                {
                  channelId: [200, 201, 203],
                  isShowItemTypeSelect: false,
                  isItemGroupSelectMultiple: true,
                },
              ],
            },
          ]}
        />
      </div>
    );
  }
}

export default Page;
