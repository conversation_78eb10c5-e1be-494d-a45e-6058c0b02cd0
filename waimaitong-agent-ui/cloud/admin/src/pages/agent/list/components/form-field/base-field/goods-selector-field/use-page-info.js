import { useEffect, useState } from 'react';

/**
 * 获取页面区间
 * @param {Object} params - 页码和每页数量参数
 * @param {number} params.current - 当前页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Array} - 起始和结束索引
 */
function getPageInterval({ current, pageSize }) {
  return [(current - 1) * pageSize, current * pageSize];
}

/**
 * 获取合法的当前页码
 * @param {Object} params - 页码和每页数量参数
 * @param {number} params.current - 当前页码
 * @param {number} params.pageSize - 每页数量
 * @param {number} total - 数据总量
 * @returns {number} - 合法的当前页码
 */
function getCurrent({ current, pageSize }, total) {
  return Math.min(current, Math.ceil(total / pageSize) || 1);
}

/**
 * 判断是否为字段数组
 * @param {any} fields - 字段对象
 * @returns {boolean} - 是否为字段数组
 */
function isFieldArray(fields) {
  return fields?._isFieldArray;
}

/**
 * 分页数据处理钩子
 * @param {Object} options - 分页参数
 * @param {number} [options.current=1] - 当前页码
 * @param {number} [options.pageSize=20] - 每页数量
 * @param {number} [options.total=0] - 数据总量
 * @param {Array} [options.items] - 数据源
 * @param {any} [options.fields] - 字段数组
 * @param {string} [options.selectedKey] - 选中项的键名
 * @param {string|number} [options.selectedValue] - 选中项的值
 * @returns {Object} - 分页相关数据和方法
 */
export default function usePageInfo({
  current = 1,
  pageSize = 20,
  total = 0,
  items,
  fields,
  selectedKey,
  selectedValue,
}) {
  const [pageInfo, setPageInfo] = useState({
    current,
    pageSize,
    total,
  });
  const [startIdx, endIdx] = getPageInterval(pageInfo);
  const datasets = isFieldArray(fields)
    ? fields.map((field, index) => ({
        index,
        field,
        fields,
        ...fields.get(index),
      }))
    : Array.isArray(items)
    ? items
    : [];

  useEffect(() => {
    if (Array.isArray(items)) {
      const { length } = items;
      if (pageInfo.total !== length) {
        mergePageInfo({
          total: length,
          current: getCurrent(pageInfo, length),
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items]);

  useEffect(() => {
    if (isFieldArray(fields)) {
      const length = fields.getAll()?.length || 0;
      if (pageInfo.total !== length) {
        mergePageInfo({
          total: length,
          current: getCurrent(pageInfo, length),
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields]);

  useEffect(() => {
    // 清空选中值，默认逻辑不会跳转到第一页
    if (selectedValue && selectedKey) {
      const idx = datasets.findIndex(
        (item) => item[selectedKey] === selectedValue
      );
      const nextPageNo = Math.floor(idx / pageInfo.pageSize) + 1;
      if (idx !== -1 && nextPageNo !== pageInfo.current) {
        mergePageInfo({
          current: nextPageNo,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedValue]);

  /**
   * 合并页码信息
   * @param {Object} newPageInfo - 新的页码信息
   */
  function mergePageInfo(newPageInfo) {
    setPageInfo((prevPageInfo) => ({
      ...prevPageInfo,
      ...newPageInfo,
    }));
  }

  return {
    pageInfo,
    onChange: mergePageInfo,
    datasets: datasets.slice(startIdx, endIdx),
  };
}
