import React, { forwardRef, useEffect, useState } from 'react';
import { FormInputField, Notify } from 'zent';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import MsgChannelField from '../base-field/msg-channel-field';
import ReceiversField from '../base-field/receivers-field';
import BaseForm from '../base-form';

const GoodsDiscardForm = forwardRef(({ yz, skill, agentId }, ref) => {
  const [loading, setLoading] = useState(false);

  const handleSubmit = (values) => {
    const params = {
      agentId,
      skills: [
        {
          id: skill.id,
          templateId: skill.skillTemplateId,
          enable: skill.enable,
          action: {
            msgChannels: values.msgChannels,
            receivers: values.receivers,
          },
          condition: {
            salesVolumeCondition: {
              period: +values.conditions[0].field,
              salesVolume: +values.conditions[0].value,
            },
          },
        },
      ],
    };
    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/saveHostAgentSkill',
        useBizRequest: true,
        method: 'post',
        data: params,
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then((res) => {
        console.log('提交商品淘汰表单数据', res);
        Notify.success('保存成功');
      })
      .catch((err) => {
        console.log('提交商品淘汰表单数据', err);
        Notify.error('保存失败');
      });
  };

  useEffect(() => {
    const { form } = ref.current;
    setLoading(true);
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/queryHostAgentSkill',
      useBizRequest: true,
      method: 'get',
      data: {
        agentId,
      },
    })
      .then((res) => {
        const { skills } = res.data.data;
        const currentSkill = skills.find(({ id }) => id === skill.id);
        if (currentSkill) {
          const { condition, action } = currentSkill;
          form.initialize({
            conditions: [
              {
                field: condition?.salesVolumeCondition?.period ?? 30,
                operator: 'lessThanOrEqual',
                value: condition?.salesVolumeCondition?.salesVolume ?? 0,
                props: {
                  valueProps: {
                    min: 0,
                    numerical: true,
                  },
                },
              },
            ],
            msgChannels: action?.msgChannels ?? [],
            receivers: action?.receivers ?? [],
          });
        }
      })
      .catch((err) => {
        console.log('获取商品淘汰表单数据', err);
        Notify.error('获取技能失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={handleSubmit} loading={loading}>
      <FieldGroup title="执行条件">
        <ConditionField
          name="conditions"
          conditionOptions={[
            { key: 30, text: '近30天销量' },
            { key: 60, text: '近60天销量' },
            { key: 90, text: '近90天销量' },
          ]}
          operatorOptions={[{ key: 'lessThanOrEqual', text: '<=' }]}
          disabled={{
            operator: true,
            remove: true,
          }}
          maxLength={1}
        />
      </FieldGroup>

      <FieldGroup title="执行操作">
        <MsgChannelField />

        <ReceiversField yz={yz} />

        <FormInputField
          name="notifyContent"
          label="通知文案："
          props={{
            placeholder: '请输入通知文案',
            rows: 4,
            type: 'textarea',
            showCount: true,
            width: 400,
            maxLength: 200,
            disabled: true,
          }}
          defaultValue="截止[XX]年[XX]月[XX]日，[XX]门店的[XX商品名称]命中[XX外卖渠道]近XX天总销量为X的规则，请及时介入处理。"
        />
      </FieldGroup>
    </BaseForm>
  );
});

export default GoodsDiscardForm;
