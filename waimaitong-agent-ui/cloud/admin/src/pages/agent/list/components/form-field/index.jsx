import React from 'react';
import EditDrawer from '../edit-drawer';
import GoodsDiscardForm from './goods-discard';
import GoodsOffShelfForm from './goods-off-shelf';
import GoodsSoldoutForm from './goods-soldout';
import GoodsSoldoutReminderForm from './goods-soldout-reminder';
import GoodsUnbindForm from './goods-unbind';
import OrderAftersaleForm from './order-aftersale';
import OrderDiningoutForm from './order-diningout';
import OrderPickupStatusForm from './order-pickup-status';
import OrderTimeoutForm from './order-timeout';
import ShopSettingForm from './shop-setting';

// 使用示例
export const ShopSettingDrawer = ({ yz, agentId, refreshSkill }) => (
  <EditDrawer title="店铺托管设置" refreshSkill={refreshSkill}>
    <ShopSettingForm yz={yz} agentId={agentId} />
  </EditDrawer>
);

export const GoodsSoldoutDrawer = ({ yz, skill, agentId, refreshSkill }) => (
  <EditDrawer title="库存售罄提醒" refreshSkill={refreshSkill}>
    <GoodsSoldoutForm yz={yz} skill={skill} agentId={agentId} />
  </EditDrawer>
);

export const GoodsSoldoutReminderDrawer = ({
  yz,
  skill,
  agentId,
  refreshSkill,
}) => (
  <EditDrawer title="库存售罄提醒" refreshSkill={refreshSkill}>
    <GoodsSoldoutReminderForm yz={yz} skill={skill} agentId={agentId} />
  </EditDrawer>
);

export const GoodsDiscardDrawer = ({ yz, skill, agentId, refreshSkill }) => (
  <EditDrawer title="商品淘汰提醒" refreshSkill={refreshSkill}>
    <GoodsDiscardForm yz={yz} skill={skill} agentId={agentId} />
  </EditDrawer>
);

export const GoodsOffShelfDrawer = ({ yz, skill, agentId, refreshSkill }) => (
  <EditDrawer title="商品下架提醒" refreshSkill={refreshSkill}>
    <GoodsOffShelfForm yz={yz} skill={skill} agentId={agentId} />
  </EditDrawer>
);

export const GoodsUnbindDrawer = ({ yz, skill, agentId, refreshSkill }) => (
  <EditDrawer title="商品异常解绑监控" refreshSkill={refreshSkill}>
    <GoodsUnbindForm yz={yz} skill={skill} agentId={agentId} />
  </EditDrawer>
);

export const OrderAftersaleDrawer = ({ yz, skill, agentId, refreshSkill }) => (
  <EditDrawer title="售后自动化任务" refreshSkill={refreshSkill}>
    <OrderAftersaleForm yz={yz} skill={skill} agentId={agentId} />
  </EditDrawer>
);

export const OrderDiningoutDrawer = ({
  yz,
  skill,
  itemSelectorRef,
  agentId,
  refreshSkill,
}) => (
  <EditDrawer title="自动出餐" width={750} refreshSkill={refreshSkill}>
    <OrderDiningoutForm
      yz={yz}
      skill={skill}
      itemSelectorRef={itemSelectorRef}
      agentId={agentId}
    />
  </EditDrawer>
);

export const OrderTimeoutDrawer = ({ yz, skill, agentId, refreshSkill }) => (
  <EditDrawer title="骑手长时间未接单" refreshSkill={refreshSkill}>
    <OrderTimeoutForm yz={yz} skill={skill} agentId={agentId} />
  </EditDrawer>
);

export const OrderPickupStatusDrawer = ({
  yz,
  skill,
  agentId,
  refreshSkill,
}) => (
  <EditDrawer title="骑手长时间未取餐" refreshSkill={refreshSkill}>
    <OrderPickupStatusForm yz={yz} skill={skill} agentId={agentId} />
  </EditDrawer>
);
