import React, { forwardRef, useEffect } from 'react';
import { FormControl, FormDescription } from 'zent';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import BaseForm from '../base-form';

const GoodsOffShelfForm = forwardRef(({ yz, skill, agentId }, ref) => {
  const handleSubmit = () => {
    const params = {
      agentId,
      skills: [
        {
          id: skill.id,
          templateId: skill.skillTemplateId,
          enable: skill.enable,
        },
      ],
    };
    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/saveHostAgentSkill',
        useBizRequest: true,
        method: 'post',
        data: params,
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then((res) => {
        console.log('提交商品下架表单数据', res);
        Notify.success('保存成功');
      })
      .catch((err) => {
        console.log('提交商品下架表单数据', err);
        Notify.error('保存失败');
      });
  };

  useEffect(() => {
    const { form } = ref.current;
    if (form) {
      form.initialize({
        conditions: [
          {
            field: 'sellCount',
            operator: 'equals',
            value: 0,
          },
        ],
      });
    }
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={handleSubmit}>
      <FieldGroup title="执行条件">
        <ConditionField
          name="conditions"
          disabled
          conditionOptions={[{ key: 'sellCount', text: '近30天销量' }]}
          operatorOptions={[{ key: 'equals', text: '=' }]}
        />
      </FieldGroup>

      <FieldGroup title="执行操作">
        <FormControl withoutLabel>
          自动下架待淘汰商品
          <FormDescription>
            下架记录可前往智能体历史执行记录查看
          </FormDescription>
        </FormControl>
      </FieldGroup>
    </BaseForm>
  );
});

export default GoodsOffShelfForm;
