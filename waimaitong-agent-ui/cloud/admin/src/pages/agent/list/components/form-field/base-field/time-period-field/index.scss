.time-period-container {
  margin-bottom: 16px;
  background-color: #F7F7F7;
  padding: 8px;
  border-radius: 4px;
}

.time-period-item {
  display: flex;
  color: #333;
  font-size: 14px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.time-period-label {
  transform: translateY(6px);
}

.time-period-content {
  display: flex;
  justify-content: space-between;
  flex: 1;
}

.time-period-picker {
  display: inline-flex !important;
}

.time-period-input {
  display: inline-flex !important;
}

.time-period-remove {
  cursor: pointer;
  color: #999;
  font-size: 20px;
  transform: translateY(5px);
}

.add-time-period-btn {
  cursor: pointer;
  color: #333;
  i {
    font-size: 16px;
  }
} 