import React, { useEffect, useState } from 'react';
import { FormSelectField } from 'zent';

const PageSize = 50;

const ReceiversField = ({ yz }) => {
  const [staffList, setStaffList] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchStaffList();
  }, []);

  const fetchStaffList = async () => {
    setLoading(true);
    const staffRes = await yz.request({
      url: '/v4/third-plugin/waimaitong-agent/getStaffList',
      useBizRequest: true,
      method: 'get',
      data: {
        pageNo: 1,
        pageSize: 1,
      },
    });

    const { totalCount } = staffRes.data.data.paginator;

    if (totalCount <= 0) {
      return;
    }

    const totalPages = Math.ceil(totalCount / PageSize);

    // 构建所有请求
    const requests = [];
    for (let pageNo = 1; pageNo <= totalPages; pageNo++) {
      requests.push(
        yz.request({
          url: '/v4/third-plugin/waimaitong-agent/getStaffList',
          useBizRequest: true,
          method: 'get',
          data: {
            pageNo,
            pageSize: PageSize,
          },
        })
      );
    }

    // 并行发送所有请求
    const responses = await Promise.all(requests);

    // 合并所有结果
    const allStaff = responses
      .reduce((acc, response) => {
        const { items } = response.data.data || response;
        return [...acc, ...(items || [])];
      }, [])
      .map((staff) => {
        return {
          text: staff.name,
          key: staff.adminId,
        };
      });

    setStaffList(allStaff);
    setLoading(false);
  };

  return (
    <FormSelectField
      name="receivers"
      label="消息接收人："
      props={{
        multiple: true,
        options: staffList,
        placeholder: '请选择消息接收人',
        // collapsable: true,
        width: 300,
        loading,
      }}
      required="请选择消息接收人"
      format={(keys = []) => {
        if (!staffList || staffList.length === 0) {
          return [];
        }
        return keys.map((key) => {
          return {
            key,
            text: staffList.find(({ key: staffKey }) => staffKey === key)?.text,
          };
        });
      }}
      normalize={(values) => values?.map(({ key }) => key) ?? []}
    />
  );
};

export default ReceiversField;
