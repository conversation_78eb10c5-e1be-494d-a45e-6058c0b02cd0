import React, { useCallback, useMemo } from 'react';
import {
  FieldSet,
  Form,
  FormInputField,
  FormNumberInputField,
  FormSelectField,
  Icon,
  Notify,
  ValidateOption,
  Validators,
} from 'zent';
import './index.scss';
import PropsField from './props-field';

function ConditionItem({
  index,
  totalItems,
  onRemove,
  disabled,
  operatorOptions,
  conditionOptions,
  model,
}) {
  const handleRemove = useCallback(() => {
    if (totalItems > 1) {
      onRemove(index);
    } else {
      Notify.error('至少保留一个执行条件');
    }
  }, [index, onRemove, totalItems]);

  const { props: currentItemProps = {} } = Form.useFieldValue(model);

  const { valueProps = {} } = currentItemProps;

  // 退款金额 隐藏 包含
  const filteredOperatorOptions = useMemo(() => {
    const { field } = model.initialValue?.value || {};
    if (field !== 'orderSource') {
      return operatorOptions.filter(option => option.disabled === false);
    }
    return operatorOptions;
  }, [operatorOptions, model]);

  return (
    <div className="condition-item">
      <div className="condition-content">
        <FormSelectField
          name="field"
          props={{
            options: conditionOptions,
            placeholder: '选择条件',
            disabled: disabled.condition || currentItemProps.fieldDisabled,
            width: 180,
          }}
          validators={[Validators.required('请选择条件')]}
          withoutLabel
          normalize={({ key }) => key}
          format={(key) => {
            if (key !== undefined && key !== '' && key !== null) {
              return {
                key,
                text: conditionOptions.find((option) => option.key === key)
                  ?.text,
              };
            }
            return null;
          }}
        />
        <FormSelectField
          name="operator"
          props={{
            options: filteredOperatorOptions,
            placeholder: '选择关系',
            disabled: disabled.operator || currentItemProps.operatorDisabled,
            width: 120,
          }}
          validators={[Validators.required('请选择关系')]}
          withoutLabel
          normalize={({ key }) => key}
          format={(key) => {
            if (key !== undefined && key !== '' && key !== null) {
              return {
                key,
                text: operatorOptions.find((option) => option.key === key)
                  ?.text,
              };
            }
            return null;
          }}
        />
        {valueProps?.numerical ? (
          <FormNumberInputField
            name="value"
            props={{
              disabled: disabled.value || valueProps.disabled,
              placeholder: '请输入值',
              width: 240,
              min: valueProps.min || -Infinity,
              max: valueProps.max || Infinity,
              integer: true,
              addonAfter: valueProps.addonAfter,
            }}
            validators={[Validators.required('请输入值')]}
            withoutLabel
          />
        ) : (
          <FormInputField
            name="value"
            props={{
              disabled: disabled.value || valueProps.disabled,
              placeholder: '请输入值',
              width: 240,
            }}
            validators={[Validators.required('请输入值')]}
            withoutLabel
          />
        )}

        {!disabled.remove && !currentItemProps.removeDisabled && (
          <Icon
            type="subtract-circle-o"
            className="condition-remove"
            onClick={handleRemove}
          />
        )}
        <PropsField />
      </div>
    </div>
  );
}

function ConditionField({
  name,
  disabled: disabledProp = false,
  operatorOptions,
  conditionOptions,
  maxLength = Infinity,
  defineAddCondition,
}) {
  const model = Form.useFieldArray(name, [
    Validators.minLength(1, '至少需要一个执行条件'),
  ]);

  const addCondition = useCallback(() => {
    const finalCondition = defineAddCondition
      ? defineAddCondition({ field: '', operator: '', value: '', props: {} })
      : { field: '', operator: '', value: '', props: {} };

    model.push(finalCondition);
    model.validate(ValidateOption.IncludeUntouched);
  }, [model]);

  const removeCondition = useCallback(
    (index) => {
      model.splice(index, 1);
      model.validate();
    },
    [model]
  );

  const disabled = useMemo(() => {
    if (typeof disabledProp === 'boolean') {
      return {
        addon: disabledProp,
        condition: disabledProp,
        remove: disabledProp,
        operator: disabledProp,
        value: disabledProp,
      };
    }
    return disabledProp;
  }, [disabledProp]);

  return (
    <>
      <div className="condition-container">
        <div className="condition-content">
          <div className="condition-joint">
            {model.children.length > 1 && (
              <div className="condition-joint-line" />
            )}
          </div>
          <div className="condition-items-wrapper">
            {model.children.map((child, index) => (
              <FieldSet key={child.id} model={child}>
                <ConditionItem
                  index={index}
                  model={child}
                  totalItems={model.children.length}
                  onRemove={removeCondition}
                  disabled={disabled}
                  operatorOptions={operatorOptions}
                  conditionOptions={conditionOptions}
                />
              </FieldSet>
            ))}
          </div>
        </div>
        {!disabled.addon && model.children.length < maxLength && (
          <span className="add-condition-btn" onClick={addCondition}>
            <Icon type="plus-circle-o" />
            添加条件
          </span>
        )}
      </div>
    </>
  );
}

export default ConditionField;
