.shop-setting-checkbox-group-field {
  .zent-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px 24px;
  }
  .zent-checkbox-wrap {
    margin: 0;
  }
}

// 库存同步规则的提示文本样式
.stockThreshold-tip {
  font-size: 12px;
  color: #999;
  margin: 4px 0 12px 24px;
  line-height: 1.4;
}

// 商品同步规则样式
.shop-setting-form {
  .field-group {
    margin-bottom: 24px;
    transition: all 0.3s ease-in-out;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 条件渲染字段的过渡动画
  .field-group-container {
    transition: all 0.3s ease-in-out;
    overflow: hidden;

    &.visible {
      opacity: 1;
      max-height: 1000px;
    }

    &.hidden {
      opacity: 0;
      max-height: 0;
      margin: 0;
      padding: 0;
    }
  }

  // 单选按钮组样式优化
  .zent-radio-group {
    .zent-radio-wrap {
      display: block;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 多选框组样式优化
  .zent-checkbox-group {
    .zent-checkbox-wrap {
      min-width: 120px;
    }
  }

  // 表单字段标签样式
  .zent-form-label {
    font-weight: 500;
  }

  // 帮助文本样式
  .zent-form-help-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-top: 4px;
  }

  // 表单字段间距优化
  .zent-form-field {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.out-item-info-sync-error-message {
  margin-left: 176px;
  color: #f40;
  font-size: 12px;
}
.zent-form-label {
  flex-basis: 168px !important; 
}