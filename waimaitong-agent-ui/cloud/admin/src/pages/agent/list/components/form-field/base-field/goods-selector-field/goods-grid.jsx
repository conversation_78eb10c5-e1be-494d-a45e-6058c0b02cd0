import React from 'react';
import { Form, Grid } from 'zent';
import usePageInfo from './use-page-info';

const GoodsGrid = () => {
  const applicableGoodsModel = Form.useField('applicableGoods');
  const { items = [] } = applicableGoodsModel.value;

  const { datasets, onChange, pageInfo } = usePageInfo({
    pageSize: 10,
    items,
  });

  // 定义列配置
  const columns = [
    {
      title: '商品',
      name: 'item',
      width: 300,
      bodyRender: ({ media, title, price }) => (
        <div className="goods-grid-item">
          <div className="goods-grid-item__image">
            {media?.images?.[0]?.url ? (
              <img src={media.images[0].url} alt={title} />
            ) : (
              <div className="goods-grid-item__placeholder" />
            )}
          </div>
          <div className="goods-grid-item__info">
            <div className="goods-grid-item__title">{title}</div>
            <div className="goods-grid-item__price">
              {`¥ ${parseFloat(price).toFixed(2)}`}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '规格',
      name: 'specs',
      bodyRender: ({ skus }) => {
        if (!skus || !skus.length) return '--';

        return skus
          .map((sku) => {
            const specs = sku.specValues || [];
            return specs.map((spec) => `${spec.specValueName}`).join('，');
          })
          .join('；');
      },
    },
    {
      title: '操作',
      name: 'operation',
      width: 100,
      align: 'right',
      bodyRender: (data) => <a onClick={() => handleDelete(data)}>删除</a>,
    },
  ];

  const handleDelete = ({ itemId, channelId }) => {
    const newItems = items.filter(
      (item) => !(item.itemId === itemId && item.channelId === channelId)
    );

    // 更新表单值
    applicableGoodsModel.patchValue({
      ...applicableGoodsModel.value,
      items: newItems,
    });
  };

  return (
    <div className="goods-grid">
      <Grid
        columns={columns}
        datasets={datasets}
        rowKey="itemId"
        emptyLabel="暂无商品数据"
        pageInfo={pageInfo}
        onChange={onChange}
        paginationType="lite"
      />

      <div className="goods-grid__footer">
        <div className="goods-grid__total">已选: 商品({items.length})</div>
      </div>
    </div>
  );
};

export default GoodsGrid;
