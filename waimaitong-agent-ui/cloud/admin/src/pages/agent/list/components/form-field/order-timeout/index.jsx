import React, { forwardRef, useEffect, useState } from 'react';
import { FormInputField, Notify } from 'zent';
import { OperatorText, OperatorType } from '../../../constants';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import <PERSON>g<PERSON><PERSON><PERSON><PERSON>ield from '../base-field/msg-channel-field';
import ReceiversField from '../base-field/receivers-field';
import BaseForm from '../base-form';

const OrderTimeoutForm = forwardRef(({ yz, skill, agentId }, ref) => {
  const [loading, setLoading] = useState(false);

  const handleSubmit = (values) => {
    const params = {
      agentId,
      skillList: [
        {
          enable: skill.enable,
          skillId: skill.id,
          skillName: skill.name,
          skillType: 3,
          skillCondition: {
            deliveryOrderingConfig: {
              waitOrderingTime: values.conditions[0].value,
              waitTimeOperatorType: 35,
              timeUnit: 1,
            },
          },
          skillAction: {
            pushAction: {
              actionType: 4,
              actionName: '消息推送',
              pushChannelList: values.msgChannels,
              staffIdList: values.receivers,
            },
          },
        },
      ],
    };

    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/createOrderSkill',
        useBizRequest: true,
        method: 'post',
        data: params,
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then((res) => {
        console.log('提交订单超时表单数据', res);
        Notify.success('保存成功');
      })
      .catch((err) => {
        console.log('提交订单超时表单数据', err);
        Notify.error('保存失败');
      });
  };

  useEffect(() => {
    const { form } = ref.current;
    setLoading(true);
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/getOrderSkill',
      useBizRequest: true,
      method: 'get',
      data: {
        agentId,
        skillId: skill.id,
      },
    })
      .then((res) => {
        const { skillList } = res.data.data;
        if (!skillList || skillList.length === 0) {
          form.initialize({
            conditions: [
              {
                field: 'deliveryTime',
                operator: OperatorType.GreaterThanOrEqual,
                value: 10,
                props: {
                  fieldDisabled: true,
                  operatorDisabled: true,
                  removeDisabled: true,
                  valueProps: {
                    min: 1,
                    max: 60,
                    addonAfter: '分钟',
                    numerical: true,
                  },
                },
              },
            ],
            msgChannels: [],
            receivers: [],
          });
          return;
        }
        const currentSkill = skillList[0];
        const {
          skillAction,
          skillCondition: { deliveryOrderingConfig },
        } = currentSkill;
        if (form) {
          // 初始化表单值
          form.initialize({
            conditions: [
              {
                field: 'deliveryTime',
                operator: OperatorType.GreaterThanOrEqual,
                value: deliveryOrderingConfig.waitOrderingTime,
                props: {
                  fieldDisabled: true,
                  operatorDisabled: true,
                  removeDisabled: true,
                  valueProps: {
                    min: 1,
                    max: 60,
                    addonAfter: '分钟',
                    numerical: true,
                  },
                },
              },
            ],
            msgChannels: skillAction.pushAction.pushChannelList,
            receivers: skillAction.pushAction.staffIdList,
          });
        }
      })
      .catch(() => {
        Notify.error('获取技能失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={handleSubmit} loading={loading}>
      <FieldGroup title="执行条件">
        <ConditionField
          name="conditions"
          conditionOptions={[{ key: 'deliveryTime', text: '骑手接单时间' }]}
          operatorOptions={[
            {
              key: OperatorType.GreaterThanOrEqual,
              text: OperatorText[OperatorType.GreaterThanOrEqual],
            },
          ]}
          disabled={{
            addon: true,
          }}
        />
      </FieldGroup>

      <FieldGroup title="执行操作">
        <MsgChannelField />

        <ReceiversField yz={yz} />

        <FormInputField
          name="notifyContent"
          label="通知文案："
          props={{
            placeholder: '请输入通知文案',
            rows: 4,
            type: 'textarea',
            showCount: true,
            width: 400,
            maxLength: 200,
            disabled: true,
          }}
          defaultValue="XX年XX月XX日，XX门店的外卖订单【XX有赞订单号】骑手长时间未接单，请及时介入处理。"
        />
      </FieldGroup>
    </BaseForm>
  );
});

export default OrderTimeoutForm;
