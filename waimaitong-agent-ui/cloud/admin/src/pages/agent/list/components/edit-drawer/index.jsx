import React, { useRef, useState } from 'react';
import { But<PERSON>, Drawer, Icon } from 'zent';
import './index.scss';

const EditDrawer = ({ title, children, width = 680, refreshSkill }) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await formRef.current?.submit?.();
      setVisible(false);
      setTimeout(() => {
        refreshSkill();
      }, 1000);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  return (
    <>
      <Icon
        className="edit-icon"
        type="edit-o"
        onClick={() => setVisible(true)}
      />
      <Drawer
        title={title}
        visible={visible}
        onClose={() => setVisible(false)}
        width={width}
        footer={
          <div className="edit-drawer-footer">
            <Button onClick={() => setVisible(false)}>取消</Button>
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              确定
            </Button>
          </div>
        }
        maskClosable
      >
        <div className="edit-drawer-content">
          {React.cloneElement(children, { ref: formRef })}
        </div>
      </Drawer>
    </>
  );
};

export default EditDrawer;
