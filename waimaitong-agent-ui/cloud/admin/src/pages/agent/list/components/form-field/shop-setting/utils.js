/**
 * 店铺托管设置数据处理工具函数
 */

import { EItemSyncRange, EStockSyncThreshold } from './types';
import { AllFieldsMap, FIELD_VISIBILITY_MAP } from './constants';

/**
 * 将旧版数据结构迁移到新版数据结构
 * @param {Object} legacyData - 旧版数据
 * @returns {Object} 新版数据结构
 */
export const migrateLegacyData = (legacyData) => {
  const {
    stockThreshold,
    outItemInfoSync: outItemInfoSyncStr,
    ...otherFields
  } = legacyData;

  let outItemInfoSync = {};
  try {
    outItemInfoSync = JSON.parse(outItemInfoSyncStr || '{}');
  } catch (err) {
    console.warn('解析旧版outItemInfoSync失败:', err);
  }

  // 构建新的商品同步规则配置
  const itemSyncRuleConfig = {
    // 如果有任何字段被选中，则默认为指定部分信息，否则为仅绑定商品
    itemSyncRange: Object.keys(outItemInfoSync).some(key => outItemInfoSync[key]) 
      ? EItemSyncRange.partial 
      : EItemSyncRange.bindOnly,
    // 库存同步规则迁移
    stockRealTimeSync: stockThreshold <= 15 ? EStockSyncThreshold.low : EStockSyncThreshold.high,
    // 默认手动同步
    itemAutoPublishSync: '0',
  };

  // 分离不同类型的字段
  const salesInfo = {};
  const baseInfo = {};
  const priceInfo = {};
  
  // 根据字段映射分类
  Object.keys(outItemInfoSync).forEach(fieldName => {
    if (outItemInfoSync[fieldName]) {
      if (AllFieldsMap.salesInfo?.includes(fieldName)) {
        salesInfo[fieldName] = 1;
      } else if (AllFieldsMap.baseInfo?.includes(fieldName)) {
        baseInfo[fieldName] = 1;
      } else if (AllFieldsMap.priceInfo?.includes(fieldName)) {
        priceInfo[fieldName] = 1;
      }
    }
  });

  return {
    ...otherFields,
    itemSyncRuleConfig: JSON.stringify(itemSyncRuleConfig),
    outItemInfoSync: JSON.stringify({
      ...salesInfo,
      ...baseInfo,
      ...priceInfo,
    }),
    headquartersModifyConfig: JSON.stringify({}), // 新字段，默认为空
  };
};

/**
 * 验证表单数据的完整性
 * @param {Object} formData - 表单数据
 * @returns {Object} 验证结果 { isValid: boolean, errors: string[] }
 */
export const validateFormData = (formData) => {
  const errors = [];
  
  // 检查必填字段
  const requiredFields = [
    'operateStatus',
    'syncBusinessHours',
    'allowOpenInvoice',
    'itemSyncRange',
    'stockRealTimeSync',
    'itemAutoPublishSync',
  ];
  
  requiredFields.forEach(field => {
    if (formData[field] === undefined || formData[field] === null || formData[field] === '') {
      errors.push(`${field} 是必填字段`);
    }
  });
  
  // 检查更新范围与其他字段的逻辑关系
  if (formData.itemSyncRange === EItemSyncRange.bindOnly) {
    // 如果选择"仅绑定商品，不更新信息"，则其他同步字段应该为空
    const syncFields = ['salesInfo', 'baseInfo', 'priceInfo'];
    syncFields.forEach(field => {
      if (formData[field] && formData[field].length > 0) {
        errors.push('选择"仅绑定商品，不更新信息"时，不应选择其他同步字段');
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * 格式化表单数据用于API提交
 * @param {Object} formData - 表单数据
 * @returns {Object} 格式化后的数据
 */
export const formatFormDataForSubmit = (formData) => {
  const {
    allowOpenInvoice,
    syncBusinessHours,
    operateStatus,
    itemSyncRange,
    salesInfo,
    baseInfo,
    priceInfo,
    stockRealTimeSync,
    itemAutoPublishSync,
    hqTemplateInfoSync,
  } = formData;

  // 构建商品同步规则配置
  const itemSyncRuleConfig = {
    itemSyncRange,
    itemAutoPublishSync,
    stockRealTimeSync: stockRealTimeSync || EStockSyncThreshold.low,
  };

  // 处理多选字段数据
  const outItemInfoSync = {};
  
  // 合并所有同步字段
  [...(salesInfo || []), ...(baseInfo || []), ...(priceInfo || [])].forEach(field => {
    outItemInfoSync[field] = 1;
  });
  
  // 处理总部可修改字段
  const headquartersModifyConfig = {};
  if (hqTemplateInfoSync && Array.isArray(hqTemplateInfoSync)) {
    hqTemplateInfoSync.forEach(field => {
      headquartersModifyConfig[field] = 1;
    });
  }

  return {
    allowOpenInvoice,
    operateStatus,
    syncBusinessHours,
    itemSyncRuleConfig: JSON.stringify(itemSyncRuleConfig),
    outItemInfoSync: JSON.stringify(outItemInfoSync),
    headquartersModifyConfig: JSON.stringify(headquartersModifyConfig),
  };
};

/**
 * 从API响应数据中提取表单初始值
 * @param {Object} apiData - API响应数据
 * @returns {Object} 表单初始值
 */
export const extractFormInitialValues = (apiData) => {
  const {
    operateStatus,
    syncBusinessHours,
    allowOpenInvoice,
    shopRole,
    itemSyncRuleConfig: itemSyncRuleConfigStr,
    outItemInfoSync: outItemInfoSyncStr,
    hqTemplateInfoSync: hqTemplateInfoSyncStr,
    // 向后兼容旧字段
    stockThreshold,
  } = apiData;

  // 安全解析JSON
  const parseJsonSafely = (jsonStr, defaultValue = {}) => {
    try {
      return jsonStr ? JSON.parse(jsonStr) : defaultValue;
    } catch (err) {
      console.warn('JSON解析失败:', err);
      return defaultValue;
    }
  };

  const itemSyncRuleConfig = parseJsonSafely(itemSyncRuleConfigStr);
  const outItemInfoSync = parseJsonSafely(outItemInfoSyncStr);
  const headquartersModifyConfig = parseJsonSafely(hqTemplateInfoSyncStr);

  // 提取已选中的字段
  const getSyncInfo = (syncData, fields = []) => {
    if (!syncData || typeof syncData !== 'object') return [];
    return fields.filter((field) => syncData[field]);
  };

  const salesInfo = getSyncInfo(outItemInfoSync, AllFieldsMap.salesInfo || []);
  const baseInfo = getSyncInfo(outItemInfoSync, AllFieldsMap.baseInfo || []);
  const priceInfo = getSyncInfo(outItemInfoSync, AllFieldsMap.priceInfo || []);

  // 根据历史数据，兼容更新范围字段的选项
  const getItemSyncRange = () => {
    if (itemSyncRuleConfig.itemSyncRange) {
      return itemSyncRuleConfig.itemSyncRange;
    }
    if (baseInfo.length || priceInfo.length) {
      return EItemSyncRange.all;
    }
    if (salesInfo.length) {
      return EItemSyncRange.partial;
    }
    return undefined;
  }


  return {
    operateStatus,
    syncBusinessHours,
    allowOpenInvoice,
    shopRole: Boolean(shopRole),
    // 商品同步规则配置
    itemSyncRange: getItemSyncRange(),
    itemAutoPublishSync: itemSyncRuleConfig.itemAutoPublishSync,
    stockRealTimeSync: itemSyncRuleConfig.stockRealTimeSync || 
                      (stockThreshold <= 15 ? EStockSyncThreshold.low : EStockSyncThreshold.high),
    // 多选字段配置
    salesInfo,
    baseInfo,
    priceInfo,
    hqTemplateInfoSync: getSyncInfo(headquartersModifyConfig, AllFieldsMap.hqTemplateInfoSync || []),
  };
};

/**
 * 计算字段可见性
 * @param {string} itemSyncRange - 更新范围值
 * @returns {Object} 字段可见性配置
 */
export const calculateFieldVisibility = (itemSyncRange) => {
  return FIELD_VISIBILITY_MAP[itemSyncRange] || FIELD_VISIBILITY_MAP[EItemSyncRange.all];
};

/**
 * 判断是否应该显示库存同步规则
 * @param {Array} salesInfo - 销售信息选中的字段
 * @param {boolean} visibleSalesInfo - 销售信息是否可见
 * @returns {boolean} 是否显示库存同步规则
 */
export const shouldShowStockSync = (salesInfo, visibleSalesInfo) => {
  return visibleSalesInfo && salesInfo && Array.isArray(salesInfo) && salesInfo.includes('stockNum');
};

/**
 * 处理表单联动副作用
 * @param {Object} formValues - 当前表单值
 * @param {string} currentRange - 当前更新范围
 * @returns {string|null} 需要更新的范围值，如果不需要更新则返回null
 */
export const handleFormSideEffects = (formValues, currentRange) => {
  const { baseInfo = [], priceInfo = [], salesInfo = [] } = formValues;

  // 检查是否需要自动调整更新范围
  // 当基础信息或价格信息有选择，但销售信息和价格信息都为空时，自动设置为"指定部分信息"
  if ((baseInfo.length > 0 || priceInfo.length > 0) &&
      salesInfo.length === 0 && priceInfo.length === 0 &&
      currentRange !== EItemSyncRange.partial) {
    return EItemSyncRange.partial;
  }

  return null; // 不需要更新
};

/**
 * 检查表单值是否为空
 * @param {Array} fieldArray - 字段数组
 * @returns {boolean} 是否为空
 */
export const isFieldArrayEmpty = (fieldArray) => {
  return !fieldArray || !Array.isArray(fieldArray) || fieldArray.length === 0;
};
