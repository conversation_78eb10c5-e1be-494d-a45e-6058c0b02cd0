/**
 * 店铺托管设置数据处理工具函数
 */

import { EItemSyncRange, EStockSyncThreshold } from './types';
import { AllFieldsMap } from './constants';

/**
 * 将旧版数据结构迁移到新版数据结构
 * @param {Object} legacyData - 旧版数据
 * @returns {Object} 新版数据结构
 */
export const migrateLegacyData = (legacyData) => {
  const {
    stockThreshold,
    outItemInfoSync: outItemInfoSyncStr,
    ...otherFields
  } = legacyData;

  let outItemInfoSync = {};
  try {
    outItemInfoSync = JSON.parse(outItemInfoSyncStr || '{}');
  } catch (err) {
    console.warn('解析旧版outItemInfoSync失败:', err);
  }

  // 构建新的商品同步规则配置
  const itemSyncRuleConfig = {
    // 如果有任何字段被选中，则默认为指定部分信息，否则为仅绑定商品
    itemSyncRange: Object.keys(outItemInfoSync).some(key => outItemInfoSync[key]) 
      ? EItemSyncRange.partial 
      : EItemSyncRange.bindOnly,
    // 库存同步规则迁移
    stockRealTimeSync: stockThreshold <= 15 ? EStockSyncThreshold.low : EStockSyncThreshold.high,
    // 默认手动同步
    autoPublishSync: '0',
  };

  // 分离不同类型的字段
  const salesInfo = {};
  const baseInfo = {};
  const priceInfo = {};
  
  // 根据字段映射分类
  Object.keys(outItemInfoSync).forEach(fieldName => {
    if (outItemInfoSync[fieldName]) {
      if (AllFieldsMap.salesInfo?.includes(fieldName)) {
        salesInfo[fieldName] = 1;
      } else if (AllFieldsMap.baseInfo?.includes(fieldName)) {
        baseInfo[fieldName] = 1;
      } else if (AllFieldsMap.priceInfo?.includes(fieldName)) {
        priceInfo[fieldName] = 1;
      }
    }
  });

  return {
    ...otherFields,
    itemSyncRuleConfig: JSON.stringify(itemSyncRuleConfig),
    outItemInfoSync: JSON.stringify({
      ...salesInfo,
      ...baseInfo,
      ...priceInfo,
    }),
    headquartersModifyConfig: JSON.stringify({}), // 新字段，默认为空
  };
};

/**
 * 验证表单数据的完整性
 * @param {Object} formData - 表单数据
 * @returns {Object} 验证结果 { isValid: boolean, errors: string[] }
 */
export const validateFormData = (formData) => {
  const errors = [];
  
  // 检查必填字段
  const requiredFields = [
    'operateStatus',
    'syncBusinessHours',
    'allowOpenInvoice',
    'itemSyncRange',
    'stockRealTimeSync',
    'autoPublishSync',
  ];
  
  requiredFields.forEach(field => {
    if (formData[field] === undefined || formData[field] === null || formData[field] === '') {
      errors.push(`${field} 是必填字段`);
    }
  });
  
  // 检查更新范围与其他字段的逻辑关系
  if (formData.itemSyncRange === EItemSyncRange.bindOnly) {
    // 如果选择"仅绑定商品，不更新信息"，则其他同步字段应该为空
    const syncFields = ['salesInfo', 'baseInfo', 'priceInfo'];
    syncFields.forEach(field => {
      if (formData[field] && formData[field].length > 0) {
        errors.push('选择"仅绑定商品，不更新信息"时，不应选择其他同步字段');
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * 格式化表单数据用于API提交
 * @param {Object} formData - 表单数据
 * @returns {Object} 格式化后的数据
 */
export const formatFormDataForSubmit = (formData) => {
  const {
    allowOpenInvoice,
    syncBusinessHours,
    operateStatus,
    itemSyncRange,
    salesInfo,
    baseInfo,
    priceInfo,
    stockRealTimeSync,
    autoPublishSync,
    headquartersModifyFields,
  } = formData;

  // 构建商品同步规则配置
  const itemSyncRuleConfig = {
    itemSyncRange,
    autoPublishSync,
    stockRealTimeSync: stockRealTimeSync || EStockSyncThreshold.low,
  };

  // 处理多选字段数据
  const outItemInfoSync = {};
  
  // 合并所有同步字段
  [...(salesInfo || []), ...(baseInfo || []), ...(priceInfo || [])].forEach(field => {
    outItemInfoSync[field] = 1;
  });
  
  // 处理总部可修改字段
  const headquartersModifyConfig = {};
  if (headquartersModifyFields && Array.isArray(headquartersModifyFields)) {
    headquartersModifyFields.forEach(field => {
      headquartersModifyConfig[field] = 1;
    });
  }

  return {
    allowOpenInvoice,
    operateStatus,
    syncBusinessHours,
    itemSyncRuleConfig: JSON.stringify(itemSyncRuleConfig),
    outItemInfoSync: JSON.stringify(outItemInfoSync),
    headquartersModifyConfig: JSON.stringify(headquartersModifyConfig),
  };
};

/**
 * 从API响应数据中提取表单初始值
 * @param {Object} apiData - API响应数据
 * @returns {Object} 表单初始值
 */
export const extractFormInitialValues = (apiData) => {
  const {
    operateStatus,
    syncBusinessHours,
    allowOpenInvoice,
    shopRole,
    itemSyncRuleConfig: itemSyncRuleConfigStr,
    outItemInfoSync: outItemInfoSyncStr,
    headquartersModifyConfig: headquartersModifyConfigStr,
    // 向后兼容旧字段
    stockThreshold,
  } = apiData;

  // 安全解析JSON
  const parseJsonSafely = (jsonStr, defaultValue = {}) => {
    try {
      return jsonStr ? JSON.parse(jsonStr) : defaultValue;
    } catch (err) {
      console.warn('JSON解析失败:', err);
      return defaultValue;
    }
  };

  const itemSyncRuleConfig = parseJsonSafely(itemSyncRuleConfigStr);
  const outItemInfoSync = parseJsonSafely(outItemInfoSyncStr);
  const headquartersModifyConfig = parseJsonSafely(headquartersModifyConfigStr);

  // 提取已选中的字段
  const getSyncInfo = (syncData, fields = []) => {
    if (!syncData || typeof syncData !== 'object') return [];
    return fields.filter((field) => syncData[field]);
  };

  return {
    operateStatus,
    syncBusinessHours,
    allowOpenInvoice,
    shopRole: Boolean(shopRole),
    // 商品同步规则配置
    itemSyncRange: itemSyncRuleConfig.itemSyncRange,
    autoPublishSync: itemSyncRuleConfig.autoPublishSync,
    stockRealTimeSync: itemSyncRuleConfig.stockRealTimeSync || 
                      (stockThreshold <= 15 ? EStockSyncThreshold.low : EStockSyncThreshold.high),
    // 多选字段配置
    salesInfo: getSyncInfo(outItemInfoSync, AllFieldsMap.salesInfo || []),
    baseInfo: getSyncInfo(outItemInfoSync, AllFieldsMap.baseInfo || []),
    priceInfo: getSyncInfo(outItemInfoSync, AllFieldsMap.priceInfo || []),
    headquartersModifyFields: getSyncInfo(headquartersModifyConfig, AllFieldsMap.headquartersModifyFields || []),
  };
};
