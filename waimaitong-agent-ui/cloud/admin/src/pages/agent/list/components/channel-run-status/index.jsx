import cx from 'classnames';
import React from 'react';
import PopWrapper from '../pop-wrapper';
import './index.scss';

const ChannelRunStatus = ({ channelDatas = [], yz }) => {
  const formatRunStatus = (runStatus) => {
    switch (runStatus) {
      case 1:
        return {
          text: '托管中',
          active: true,
        };
      case 2:
        return {
          text: '暂停托管',
          active: false,
        };
      case 3:
        return {
          text: '未绑定',
          active: false,
        };
      default:
        return {
          text: '未绑定',
          active: false,
        };
    }
  };

  return (
    <div className="list-desc">
      <div className="list-desc-title">描述</div>
      <div className="list-desc-content">
        7*24h无间断托管, 具有全外卖渠道店铺、商品、订单模块的核心技能，
        {/* <a
          href="https://www.baidu.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          查看托管方案
        </a> */}
      </div>
      <div className="list-desc-divider" />
      <div className="list-desc-title">渠道管理</div>
      <div className="list-desc-channel">
        {channelDatas.map((item) => (
          <PopWrapper
            shouldWrap={!formatRunStatus(item.runStatus).active}
            trigger="hover"
            position="top-left"
            key={item.channelId}
            content={
              <span>
                当前渠道未开通，
                <a
                  onClick={() => {
                    yz.navigateTo({
                      route:
                        'https://store.youzan.com/v4/channel/omni/omni-channel#/auth',
                      blank: true,
                    });
                  }}
                  style={{
                    cursor: 'pointer',
                  }}
                >
                  前往开通
                </a>
              </span>
            }
          >
            <div
              className={cx('list-desc-channel-item', {
                active: formatRunStatus(item.runStatus).active,
              })}
            >
              <img src={item.channelLogo} />
              <span>{formatRunStatus(item.runStatus).text}</span>
            </div>
          </PopWrapper>
        ))}
      </div>
    </div>
  );
};

export default ChannelRunStatus;
