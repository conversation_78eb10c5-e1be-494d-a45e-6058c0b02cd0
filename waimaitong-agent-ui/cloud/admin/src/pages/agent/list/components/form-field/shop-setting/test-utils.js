/**
 * 店铺托管设置功能测试工具
 * 用于验证新功能的正确性
 */

import { 
  calculateFieldVisibility, 
  shouldShowStockSync, 
  handleFormSideEffects,
  isFieldArrayEmpty 
} from './utils';
import { EItemSyncRange } from './types';

/**
 * 测试字段可见性计算
 */
export const testFieldVisibility = () => {
  console.log('=== 测试字段可见性计算 ===');
  
  // 测试全部信息
  const allFields = calculateFieldVisibility(EItemSyncRange.all);
  console.log('全部信息模式:', allFields);
  console.assert(allFields.salesInfo === true, '全部信息模式应显示销售信息');
  console.assert(allFields.baseInfo === true, '全部信息模式应显示基础信息');
  console.assert(allFields.priceInfo === true, '全部信息模式应显示价格信息');
  
  // 测试指定部分信息
  const partialFields = calculateFieldVisibility(EItemSyncRange.partial);
  console.log('指定部分信息模式:', partialFields);
  console.assert(partialFields.salesInfo === true, '指定部分信息模式应显示销售信息');
  console.assert(partialFields.baseInfo === false, '指定部分信息模式不应显示基础信息');
  console.assert(partialFields.priceInfo === false, '指定部分信息模式不应显示价格信息');
  
  // 测试仅绑定商品
  const bindOnlyFields = calculateFieldVisibility(EItemSyncRange.bindOnly);
  console.log('仅绑定商品模式:', bindOnlyFields);
  console.assert(bindOnlyFields.salesInfo === false, '仅绑定商品模式不应显示销售信息');
  console.assert(bindOnlyFields.baseInfo === false, '仅绑定商品模式不应显示基础信息');
  console.assert(bindOnlyFields.priceInfo === false, '仅绑定商品模式不应显示价格信息');
  console.assert(bindOnlyFields.itemAutoPublishSync === true, '仅绑定商品模式应显示自动发布配置');
  
  console.log('✅ 字段可见性计算测试通过');
};

/**
 * 测试库存同步规则显示逻辑
 */
export const testStockSyncDisplay = () => {
  console.log('=== 测试库存同步规则显示逻辑 ===');
  
  // 测试显示库存同步规则
  const shouldShow1 = shouldShowStockSync(['stockNum'], true);
  console.assert(shouldShow1 === true, '选中库存且销售信息可见时应显示库存同步规则');
  
  // 测试不显示库存同步规则
  const shouldShow2 = shouldShowStockSync(['takeUpOrDown'], true);
  console.assert(shouldShow2 === false, '未选中库存时不应显示库存同步规则');
  
  const shouldShow3 = shouldShowStockSync(['stockNum'], false);
  console.assert(shouldShow3 === false, '销售信息不可见时不应显示库存同步规则');
  
  const shouldShow4 = shouldShowStockSync(null, true);
  console.assert(shouldShow4 === false, '销售信息为空时不应显示库存同步规则');
  
  console.log('✅ 库存同步规则显示逻辑测试通过');
};

/**
 * 测试表单联动副作用
 */
export const testFormSideEffects = () => {
  console.log('=== 测试表单联动副作用 ===');
  
  // 测试需要自动调整的情况
  const formValues1 = {
    baseInfo: ['title'],
    priceInfo: [],
    salesInfo: [],
  };
  const result1 = handleFormSideEffects(formValues1, EItemSyncRange.all);
  console.assert(result1 === EItemSyncRange.partial, '基础信息有选择但销售信息为空时应自动调整为指定部分信息');
  
  const formValues2 = {
    baseInfo: [],
    priceInfo: ['price'],
    salesInfo: [],
  };
  const result2 = handleFormSideEffects(formValues2, EItemSyncRange.all);
  console.assert(result2 === EItemSyncRange.partial, '价格信息有选择但销售信息为空时应自动调整为指定部分信息');
  
  // 测试不需要调整的情况
  const formValues3 = {
    baseInfo: ['title'],
    priceInfo: [],
    salesInfo: ['stockNum'],
  };
  const result3 = handleFormSideEffects(formValues3, EItemSyncRange.all);
  console.assert(result3 === null, '销售信息有选择时不应自动调整');
  
  const formValues4 = {
    baseInfo: [],
    priceInfo: [],
    salesInfo: [],
  };
  const result4 = handleFormSideEffects(formValues4, EItemSyncRange.all);
  console.assert(result4 === null, '所有信息都为空时不应自动调整');
  
  console.log('✅ 表单联动副作用测试通过');
};

/**
 * 测试字段数组空值检查
 */
export const testFieldArrayEmpty = () => {
  console.log('=== 测试字段数组空值检查 ===');
  
  console.assert(isFieldArrayEmpty(null) === true, 'null应被认为是空');
  console.assert(isFieldArrayEmpty(undefined) === true, 'undefined应被认为是空');
  console.assert(isFieldArrayEmpty([]) === true, '空数组应被认为是空');
  console.assert(isFieldArrayEmpty(['item']) === false, '有元素的数组不应被认为是空');
  console.assert(isFieldArrayEmpty('string') === true, '非数组类型应被认为是空');
  
  console.log('✅ 字段数组空值检查测试通过');
};

/**
 * 运行所有测试
 */
export const runAllTests = () => {
  console.log('🚀 开始运行店铺托管设置功能测试...\n');
  
  try {
    testFieldVisibility();
    testStockSyncDisplay();
    testFormSideEffects();
    testFieldArrayEmpty();
    
    console.log('\n🎉 所有测试通过！新功能实现正确。');
  } catch (error) {
    console.error('\n❌ 测试失败:', error);
  }
};

// 如果在浏览器环境中，可以通过控制台运行测试
if (typeof window !== 'undefined') {
  window.testShopSetting = {
    runAllTests,
    testFieldVisibility,
    testStockSyncDisplay,
    testFormSideEffects,
    testFieldArrayEmpty,
  };
}
