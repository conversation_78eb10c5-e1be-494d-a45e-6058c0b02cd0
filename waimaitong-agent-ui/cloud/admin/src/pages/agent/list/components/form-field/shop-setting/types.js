/**
 * 店铺托管设置相关的类型定义
 */

// 更新范围枚举
export const EItemSyncRange = {
  all: 0,           // 全部信息
  partial: 1,       // 指定部分信息
  bindOnly: 2,      // 仅绑定商品，不更新信息
};

// 商品库发布外卖商品时枚举
export const EAutoPublishSync = {
  auto: 1,          // 自动发布创建到外卖平台
  manual: 0,        // 手动同步创建到外卖平台
};

// 库存同步规则枚举
export const EStockSyncThreshold = {
  low: '15',          // 当商品增加库存，或可售库存≤15个时，实时同步
  high: '50',         // 当商品增加库存，或可售库存≤50个时，实时同步
};

// 表单字段名称常量
export const FORM_FIELDS = {
  // 基础店铺设置
  operateStatus: 'operateStatus',
  syncBusinessHours: 'syncBusinessHours',
  allowOpenInvoice: 'allowOpenInvoice',
  
  // 商品同步规则
  itemSyncRange: 'itemSyncRange',
  salesInfo: 'salesInfo',
  baseInfo: 'baseInfo',
  priceInfo: 'priceInfo',
  stockThreshold: 'stockThreshold',
  itemAutoPublishSync: 'itemAutoPublishSync',
  hqTemplateInfoSync: 'hqTemplateInfoSync',
};

// 字段选项配置
export const FIELD_OPTIONS = {
  // 销售信息选项
  salesInfo: [
    { value: 'takeUpOrDown', label: '销售状态（上下架）' },
    { value: 'stockNum', label: '库存' },
  ],
  
  // 基础信息选项
  baseInfo: [
    { value: 'title', label: '商品名称' },
    { value: 'skuName', label: '规格名称' },
    { value: 'barcode', label: '商品/规格条码' },
    { value: 'category', label: '商品类目' },
    { value: 'prop', label: '属性' },
    { value: 'picture', label: '商品主图' },
    { value: 'group', label: '商品分组' },
    { value: 'sellPoint', label: '商品描述' },
    { value: 'minimumPurchaseNum', label: '最小起购量' },
  ],
  
  // 价格信息选项
  priceInfo: [
    { value: 'price', label: '价格' },
    { value: 'packingCharge', label: '商品打包费' },
  ],
  
  // 总部可修改分店外卖商品选项
  hqTemplateInfoSync: [
    { value: 'packingCharge', label: '商品打包费' },
    { value: 'minimumPurchaseNum', label: '最小起购量' },
    { value: 'title', label: '商品名称' },
    { value: 'group', label: '商品分组' },
    { value: 'price', label: '价格' },
    { value: 'skuDisableStatus', label: '规格启/禁用' },
  ],
};
