import React, { forwardRef, useEffect, useState } from 'react';
import { FormDescription, Icon, Notify } from 'zent';
import FieldGroup from '../../field-group';
import GoodsSelectorField from '../base-field/goods-selector-field';
import TimePeriodField from '../base-field/time-period-field';
import BaseForm from '../base-form';
import './index.scss';

const OrderDiningoutForm = forwardRef(
  ({ yz, skill, itemSelectorRef, agentId }, ref) => {
    const [loading, setLoading] = useState(false);

    const handleSubmit = (values) => {
      const params = {
        agentId,
        skillList: [
          {
            enable: skill.enable,
            skillId: skill.id,
            skillName: skill.name,
            skillType: 1,
            skillAction: {
              autoConfirmAction: {
                actionName: '自动出餐',
                actionType: 1,
              },
            },
            skillCondition: {
              instantOrderConfig: values.instantPeriods.map((item) => ({
                startTime: item.timeRange[0],
                endTime: item.timeRange[1],
                offsetTime: item.delay,
                offsetTimeType: 2,
                timeUnit: 1,
              })),
              appointmentOrderConfig: values.reservationPeriods.map((item) => ({
                startTime: item.timeRange[0],
                endTime: item.timeRange[1],
                offsetTime: item.delay,
                offsetTimeType: 2,
                timeUnit: 1,
              })),
              applicableGoodsConfig: {
                applicableType: values.applicableGoods.applicableType,
                applicableGoodsIdList: values.applicableGoods.itemIds,
              },
            },
          },
        ],
      };

      return yz
        .request({
          url: '/v4/third-plugin/waimaitong-agent/createOrderSkill',
          useBizRequest: true,
          method: 'post',
          data: params,
          header: {
            'Content-Type': 'application/json',
          },
        })
        .then((res) => {
          console.log('提交外卖出餐设置数据', res);
          Notify.success('保存成功');
        })
        .catch((err) => {
          console.log('提交外卖出餐设置数据失败', err);
          Notify.error('保存失败');
        });
    };

    useEffect(() => {
      const { form } = ref.current;

      setLoading(true);
      yz.request({
        url: '/v4/third-plugin/waimaitong-agent/getOrderSkill',
        useBizRequest: true,
        method: 'get',
        data: {
          agentId,
          skillId: skill.id,
        },
      })
        .then((res) => {
          const { skillList } = res.data.data;

          if (!skillList || skillList.length === 0) {
            form.initialize({
              instantPeriods: [],
              reservationPeriods: [],
              applicableGoods: {
                applicableType: 1,
                itemIds: [],
              },
            });
            return;
          }

          const currentSkill = skillList[0];
          const {
            instantOrderConfig,
            appointmentOrderConfig,
            applicableGoodsConfig = {
              applicableType: 1,
              applicableGoodsIdList: [],
            },
          } = currentSkill.skillCondition;
          if (form) {
            // 初始化表单值
            form.initialize({
              instantPeriods: instantOrderConfig.map((item) => ({
                delay: item.offsetTime,
                timeRange: [item.startTime, item.endTime],
              })),
              reservationPeriods: appointmentOrderConfig.map((item) => ({
                delay: item.offsetTime,
                timeRange: [item.startTime, item.endTime],
              })),
              applicableGoods: {
                applicableType: applicableGoodsConfig.applicableType,
                itemIds: applicableGoodsConfig.applicableGoodsIdList,
              },
            });
          }
        })
        .catch(() => {
          Notify.error('获取技能失败');
        })
        .finally(() => {
          setLoading(false);
        });
    }, []);

    return (
      <BaseForm ref={ref} onSubmit={handleSubmit} loading={loading}>
        <FormDescription className="order-diningout-form-description">
          <Icon type="info-circle-o" />
          支持智能出餐，省去人工手动操作，如需临时暂停请及时关闭技能。
          <a
            onClick={() => {
              yz.navigateTo({
                route:
                  'https://bbs.youzan.com/forum.php?mod=viewthread&tid=699480',
                blank: true,
              });
            }}
          >
            查看操作示例
          </a>
        </FormDescription>

        <FieldGroup title="即时单时段设置">
          <TimePeriodField
            name="instantPeriods"
            periodLabel="时段"
            afterTimeLabel="接单后"
            maxLength={5}
          />
        </FieldGroup>

        <FieldGroup title="预定单时段设置">
          <TimePeriodField
            name="reservationPeriods"
            periodLabel="时段"
            afterTimeLabel="预约送达时间前"
            maxLength={5}
          />
        </FieldGroup>
        <FieldGroup
          title="适用商品设置"
          description="只有订单中的商品全部是自动出餐商品时，此订单才会自动出餐"
        >
          <GoodsSelectorField itemSelectorRef={itemSelectorRef} />
          <FormDescription className="order-diningout-bottom-description">
            <Icon type="warning" />
            智能出餐无法完全规避潜在风险，任何风险及相应责任商户开启功能后均由商户自行承担，线下请按时间要求出餐。
          </FormDescription>
        </FieldGroup>
      </BaseForm>
    );
  }
);

export default OrderDiningoutForm;
