import React, { forwardRef, useEffect, useState } from 'react';
import { FormInputField, Notify } from 'zent';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import MsgChannelField from '../base-field/msg-channel-field';
import ReceiversField from '../base-field/receivers-field';
import BaseForm from '../base-form';

const GoodsSoldoutForm = forwardRef(({ yz, skill, agentId }, ref) => {
  const [loading, setLoading] = useState(false);

  const handleSubmit = (values) => {
    const params = {
      agentId,
      skills: [
        {
          id: skill.id,
          templateId: skill.skillTemplateId,
          enable: skill.enable,
          action: {
            msgChannels: values.msgChannels,
            receivers: values.receivers,
          },
        },
      ],
    };
    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/saveHostAgentSkill',
        useBizRequest: true,
        method: 'post',
        data: params,
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then((res) => {
        console.log('提交商品下架表单数据', res);
        Notify.success('保存成功');
      })
      .catch((err) => {
        console.log('提交商品下架表单数据', err);
        Notify.error('保存失败');
      });
  };

  useEffect(() => {
    const { form } = ref.current;
    if (form) {
      form.initialize({
        conditions: [
          {
            field: 'stock',
            operator: 'equals',
            value: 0,
          },
        ],
      });
    }
    setLoading(true);
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/queryHostAgentSkill',
      useBizRequest: true,
      method: 'get',
      data: {
        agentId,
      },
    })
      .then((res) => {
        const { skills } = res.data.data;
        const currentSkill = skills.find(({ id }) => id === skill.id);
        if (currentSkill) {
          form.patchValue({
            msgChannels: currentSkill.action?.msgChannels ?? [],
            receivers: currentSkill.action?.receivers ?? [],
          });
        }
      })
      .catch((err) => {
        console.log('获取商品下架表单数据', err);
        Notify.error('获取技能失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={handleSubmit} loading={loading}>
      <FieldGroup title="执行条件">
        <ConditionField
          name="conditions"
          disabled
          operatorOptions={[{ key: 'equals', text: '=' }]}
          conditionOptions={[{ key: 'stock', text: '库存数量' }]}
        />
      </FieldGroup>

      <FieldGroup title="执行操作">
        <MsgChannelField />

        <ReceiversField yz={yz} />

        <FormInputField
          name="notifyContent"
          label="通知文案："
          props={{
            placeholder: '请输入通知文案',
            rows: 4,
            type: 'textarea',
            showCount: true,
            width: 400,
            maxLength: 200,
            disabled: true,
          }}
          defaultValue="[XX]年[XX]月[XX]日[XX]分，[XX]门店的[XX外卖渠道][XX商品名称]已售罄，请知悉。"
        />
      </FieldGroup>
    </BaseForm>
  );
});

export default GoodsSoldoutForm;
