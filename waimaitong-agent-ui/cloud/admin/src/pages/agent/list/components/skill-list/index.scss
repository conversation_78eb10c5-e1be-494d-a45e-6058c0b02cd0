.skill-list {
  border-radius: 8px;
  background-color: #F7F7F7;
}

// SkillHeader 样式
.skill-header {
  line-height: 22px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  color: #333;

  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    line-height: 24px;
  }
}

.skill-items {
  &.no-header {
    .skill-item {
      margin: 0;
    }
  }
}

.skill-item {
  display: flex;
  justify-content: space-between;
  margin: 16px 0;

  &:hover {
    .skill-item-actions {
      visibility: visible;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }


  &-info {
    display: flex;
    align-items: center;
    gap: 16px;
    line-height: 20px;
    margin-bottom: 8px;
  }

  &-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  &-status {
    position: relative;
    padding-left: 12px;
    color: #999;
    font-weight: 500;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #999;
    }
    
    &.running {
      color: #45a110;
      &::before {
        background-color: #45a110;
      }
    }
  }

  &-desc {
    font-size: 14px;
    color: #999;
    line-height: 20px;
  }

  &-actions {
    display: flex;
    gap: 12px;
    visibility: hidden;
    i {
      cursor: pointer;
      font-size: 20px;
    }
    img {
      display: block;
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
} 