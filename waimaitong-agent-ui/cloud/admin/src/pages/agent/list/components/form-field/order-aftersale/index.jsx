import React, { forwardRef, useEffect, useState } from 'react';
import { FormDescription, Notify } from 'zent';
import { OperatorText, OperatorType } from '../../../constants';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import BaseForm from '../base-form';

const conditionOptions = [
  { key: 'deliveryStatus', text: '发货状态', disabled: true },
  { key: 'orderSource', text: '订单来源', disabled: true },
  { key: 'refundAmount', text: '退款金额' },
];

const operatorOptions = Object.entries(OperatorType).map(([, text]) => {
  return {
    key: text,
    text: OperatorText[text],
    disabled: text === OperatorType.In,
  };
});

const OrderAftersaleForm = forwardRef(({ yz, skill, agentId }, ref) => {
  const [loading, setLoading] = useState(false);

  const buildConditionConfig = (values) => {
    const { conditions } = values;
    const result = {};
    const deliveryStatusCondition = conditions.find(
      (item) => item.field === 'deliveryStatus'
    );

    const orderSourceCondition = conditions.find(
      (item) => item.field === 'orderSource'
    );

    const refundAmountCondition = conditions.find(
      (item) => item.field === 'refundAmount'
    );

    result.shippingConfig = {
      operatorType: deliveryStatusCondition.operator,
      orderStatus: 40,
    };

    result.sourceConfig = {
      operatorType: orderSourceCondition.operator,
      salesChannelId: [20, 120, 130, 140],
    };

    if (refundAmountCondition) {
      result.amountMaxConfig = {
        operatorType: refundAmountCondition.operator,
        amountValue: refundAmountCondition.value * 1000,
      };
    }

    return result;
  };

  const handleSubmit = (values) => {
    const params = {
      agentId,
      skillList: [
        {
          enable: skill.enable,
          skillId: skill.id,
          skillName: skill.name,
          skillType: 2,
          skillAction: {
            autoRefundAction: {
              actionName: '自动退款',
              actionType: 2,
            },
          },
          skillCondition: buildConditionConfig(values),
        },
      ],
    };

    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/createOrderSkill',
        useBizRequest: true,
        method: 'post',
        data: params,
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then((res) => {
        console.log('提交售后表单数据', res);
        Notify.success('保存成功');
      })
      .catch((err) => {
        console.log('提交售后表单数据失败', err);
        Notify.error('保存失败');
      });
  };

  useEffect(() => {
    const { form } = ref.current;
    if (form) {
      form.initialize({
        conditions: [
          {
            field: 'deliveryStatus',
            operator: OperatorType.Equals,
            value: '未发货',
            props: {
              fieldDisabled: true,
              operatorDisabled: true,
              removeDisabled: true,
              valueProps: {
                disabled: true,
              },
            },
          },
          {
            field: 'orderSource',
            operator: OperatorType.In,
            value: '全部外卖渠道',
            props: {
              fieldDisabled: true,
              operatorDisabled: true,
              removeDisabled: true,
              valueProps: {
                disabled: true,
              },
            },
          },
        ],
      });
    }

    setLoading(true);
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/getOrderSkill',
      useBizRequest: true,
      method: 'get',
      data: {
        agentId,
        skillId: skill.id,
      },
    })
      .then((res) => {
        console.log('获取外卖出餐设置数据', res);
        const { skillList } = res.data.data;
        if (skillList.length > 0) {
          const currentSkill = skillList[0];
          const { amountMaxConfig } = currentSkill.skillCondition;
          if (form) {
            // 初始化表单值
            form.patchValue({
              conditions: [
                ...form.getValue().conditions,
                ...(amountMaxConfig
                  ? [
                      {
                        field: 'refundAmount',
                        operator: amountMaxConfig.operatorType,
                        value: amountMaxConfig.amountValue / 1000
                      }
                    ]
                  : [])
              ]
            });
          }
        }
      })
      .catch((err) => {
        console.log('获取技能失败', err);
        Notify.error('获取技能失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={handleSubmit} loading={loading}>
      <FieldGroup title="执行条件">
        <ConditionField
          name="conditions"
          conditionOptions={conditionOptions}
          operatorOptions={operatorOptions}
          maxLength={3}
          defineAddCondition={() => ({
            props: {
              valueProps: {
                min: 0,
                max: 10e6 - 1,
                addonAfter: '元',
                numerical: true,
              },
            },
          })}
        />
      </FieldGroup>

      <FieldGroup title="执行操作">
        <FormDescription>自动同意消费者取消订单</FormDescription>
      </FieldGroup>
    </BaseForm>
  );
});

export default OrderAftersaleForm;
