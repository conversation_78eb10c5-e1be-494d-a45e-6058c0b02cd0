import React, { forwardRef, useEffect, useState } from 'react';
import { FormInputField, FormRadioGroupField, Notify, Radio } from 'zent';
import FieldGroup from '../../field-group';
import MsgChan<PERSON><PERSON>ield from '../base-field/msg-channel-field';
import ReceiversField from '../base-field/receivers-field';
import BaseForm from '../base-form';

const GoodsUnbindForm = forwardRef(({ yz, skill, agentId }, ref) => {
  const [loading, setLoading] = useState(false);

  const handleSubmit = (values) => {
    const params = {
      agentId,
      skills: [
        {
          id: skill.id,
          templateId: skill.skillTemplateId,
          enable: skill.enable,
          action: {
            msgChannels: values.msgChannels,
            receivers: values.receivers,
          },
        },
      ],
    };
    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/saveHostAgentSkill',
        useBizRequest: true,
        method: 'post',
        data: params,
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then((res) => {
        console.log('提交商品淘汰表单数据', res);
        Notify.success('保存成功');
      })
      .catch((err) => {
        console.log('提交商品淘汰表单数据', err);
        Notify.error('保存失败');
      });
  };

  useEffect(() => {
    const { form } = ref.current;
    if (form) {
      form.initialize({
        notifyType: 1,
      });
    }
    setLoading(true);
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/queryHostAgentSkill',
      useBizRequest: true,
      method: 'get',
      data: {
        agentId,
      },
    })
      .then((res) => {
        const { skills } = res.data.data;
        const currentSkill = skills.find(({ id }) => id === skill.id);
        form.patchValue({
          msgChannels: currentSkill.action?.msgChannels ?? [],
          receivers: currentSkill.action?.receivers ?? [],
        });
      })
      .catch((err) => {
        console.log('获取商品下架表单数据', err);
        Notify.error('获取技能失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={handleSubmit} loading={loading}>
      <FieldGroup title="当系统检测到外卖店铺下非人为直接操作导致已绑定的商品被解绑，默认处理方式：">
        <FormRadioGroupField
          name="notifyType"
          required
          helpDesc="商品绑定关系解除将直接影响商品库存同步"
          withoutLabel
        >
          <Radio value={1}>通知店铺管理员</Radio>
        </FormRadioGroupField>
      </FieldGroup>

      <FieldGroup title="执行操作">
        <MsgChannelField />

        <ReceiversField yz={yz} />

        <FormInputField
          name="notifyContent"
          label="通知文案："
          props={{
            placeholder: '请输入通知文案',
            rows: 4,
            type: 'textarea',
            showCount: true,
            width: 400,
            maxLength: 200,
            disabled: true,
          }}
          defaultValue="[XX]年[XX]月[XX]日[XX]分，[XX]门店的[XX外卖渠道][XX商品名称]监控到异常解绑，请及时介入处理。"
        />
      </FieldGroup>
    </BaseForm>
  );
});

export default GoodsUnbindForm;
