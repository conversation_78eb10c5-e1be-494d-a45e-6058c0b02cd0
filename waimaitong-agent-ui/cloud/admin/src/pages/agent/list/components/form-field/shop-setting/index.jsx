import React, { forwardRef, useEffect, useState, useCallback } from 'react';
import {
  Checkbox,
  FormCheckboxGroupField,
  FormRadioGroupField,
  Notify,
  Radio,
} from 'zent';
import FieldGroup from '../../field-group';
import BaseForm from '../base-form';
import {
  ItemSyncRuleMap,
  HeadquartersModifyFieldsMap,
  ItemSyncRangeOptions,
  AutoPublishSyncOptions,
  StockSyncThresholdOptions,
} from './constants';
import { EStockSyncThreshold } from './types';
import { validateFormData, formatFormDataForSubmit, extractFormInitialValues } from './utils';
import './index.scss';

const ShopSettingForm = forwardRef(({ yz }, ref) => {
  const [loading, setLoading] = useState(true);
  const [shopRole, setShopRole] = useState(false);

  const handleSubmit = useCallback((values) => {
    // 验证表单数据
    const validation = validateFormData(values);
    if (!validation.isValid) {
      validation.errors.forEach(error => {
        Notify.error(error);
      });
      return Promise.reject(new Error('表单验证失败'));
    }

    // 格式化数据
    const params = formatFormDataForSubmit(values);

    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/updateWmShopHostConfig',
        useBizRequest: true,
        method: 'post',
        data: { channelAiConfigDetailDTO: params },
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        Notify.success('保存成功');
      })
      .catch((err) => {
        console.error('保存失败:', err);
        Notify.error('保存失败');
      });
  }, [yz]);

  useEffect(() => {
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/queryWmShopHostConfig',
      useBizRequest: true,
      method: 'get',
    })
      .then((res) => {
        const form = ref.current?.form;
        const { channelAiConfigDetailDTO } = res.data.data;

        if (form) {
          // 使用工具函数提取表单初始值
          const formData = extractFormInitialValues(channelAiConfigDetailDTO);

          // 初始化表单
          form.initialize(formData);

          // 设置店铺角色状态
          setShopRole(formData.shopRole);
        }
      })
      .catch((err) => {
        console.error('获取店铺设置失败:', err);
        Notify.error('获取店铺设置失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [yz]);

  return (
    <BaseForm loading={loading} ref={ref} onSubmit={handleSubmit} className="shop-setting-form">
      <FieldGroup title="店铺">
        <FormRadioGroupField
          name="operateStatus"
          label="店铺经营状态："
          required
          helpDesc="设置外部渠道的店铺经营状态，保存后系统会自动更改营业状态。"
        >
          <Radio value={1}>营业</Radio>
          <Radio value={0}>休息</Radio>
        </FormRadioGroupField>

        <FormRadioGroupField
          name="syncBusinessHours"
          label="营业时间同步："
          required
          helpDesc={
            <>
              将有赞店铺已有营业时间同步至外部店铺
              {shopRole && (
                <>
                  ，可在"
                  <a
                    onClick={() => {
                      yz.navigateTo({
                        route:
                          'https://store.youzan.com/v2/retailshop/org-manage',
                        blank: true,
                      });
                    }}
                  >
                    店铺-组织机构
                  </a>
                  "中设置有赞店铺营业时间。
                </>
              )}
              <br />
              支持每天重复的营业时间同步，每周重复设置暂不支持。
            </>
          }
        >
          <Radio value={1}>同步</Radio>
          <Radio value={0}>不同步</Radio>
        </FormRadioGroupField>
      </FieldGroup>

      <FieldGroup title="商品同步规则">
        {/* 1. 更新范围 */}
        <FormRadioGroupField
          name="itemSyncRange"
          label="更新范围："
          required
          helpDesc="选择商品信息的同步范围"
        >
          {ItemSyncRangeOptions.map((option) => (
            <Radio key={option.value} value={option.value}>
              {option.label}
            </Radio>
          ))}
        </FormRadioGroupField>

        {/* 2. 销售信息 */}
        <FormCheckboxGroupField
          name="salesInfo"
          label="销售信息："
          className="shop-setting-checkbox-group-field"
        >
          {ItemSyncRuleMap.find(group => group.group === 'salesInfo')?.fields.map((field) => (
            <Checkbox key={field.value} value={field.value}>
              {field.label}
            </Checkbox>
          ))}
        </FormCheckboxGroupField>

        {/* 3. 基础信息 */}
        <FormCheckboxGroupField
          name="baseInfo"
          label="基础信息："
          className="shop-setting-checkbox-group-field"
        >
          {ItemSyncRuleMap.find(group => group.group === 'baseInfo')?.fields.map((field) => (
            <Checkbox key={field.value} value={field.value}>
              {field.label}
            </Checkbox>
          ))}
        </FormCheckboxGroupField>

        {/* 4. 价格信息 */}
        <FormCheckboxGroupField
          name="priceInfo"
          label="价格信息："
          className="shop-setting-checkbox-group-field"
        >
          {ItemSyncRuleMap.find(group => group.group === 'priceInfo')?.fields.map((field) => (
            <Checkbox key={field.value} value={field.value}>
              {field.label}
            </Checkbox>
          ))}
        </FormCheckboxGroupField>

        {/* 5. 库存同步规则 */}
        <FormRadioGroupField
          name="stockRealTimeSync"
          label="库存同步规则："
          required
          defaultValue={EStockSyncThreshold.low}
        >
          {StockSyncThresholdOptions.map((option) => (
            <Radio key={option.value} value={option.value}>
              {option.label}
              <div className="stockRealTimeSync-tip">
                {option.tip}
              </div>
            </Radio>
          ))}
        </FormRadioGroupField>

        {/* 6. 商品库发布外卖商品时 */}
        <FormRadioGroupField
          name="autoPublishSync"
          label="商品库发布外卖商品时："
          required
        >
          {AutoPublishSyncOptions.map((option) => (
            <Radio key={option.value} value={option.value}>
              {option.label}
            </Radio>
          ))}
        </FormRadioGroupField>

        {/* 7. 总部可修改分店外卖商品 */}
        <FormCheckboxGroupField
          name="headquartersModifyFields"
          label="总部可修改分店外卖商品："
          className="shop-setting-checkbox-group-field"
          helpDesc="默认总部强管控,商品库更新将覆盖分店外卖渠道商品信息非总部强管控可取消勾选。"
        >
          {HeadquartersModifyFieldsMap.fields.map((field) => (
            <Checkbox key={field.value} value={field.value}>
              {field.label}
            </Checkbox>
          ))}
        </FormCheckboxGroupField>
      </FieldGroup>

      <FieldGroup title="外卖开票设置">
        <FormRadioGroupField
          name="allowOpenInvoice"
          label="扫小票二维码开票："
          required
          helpDesc={`开启后，可通过扫外卖订单小票"开票二维码"开具发票。请务必前里准定有效商品和外卖渠道通商品，否则无法开票。`}
        >
          <Radio value={true}>开启</Radio>
          <Radio value={false}>关闭</Radio>
        </FormRadioGroupField>
      </FieldGroup>
    </BaseForm>
  );
});

export default ShopSettingForm;
