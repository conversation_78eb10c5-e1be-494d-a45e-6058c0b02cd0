import { forwardRef, useEffect, useState, useCallback, useMemo } from 'react';
import {
  Checkbox,
  FormCheckboxGroupField,
  FormRadioGroupField,
  Notify,
  Radio,
} from 'zent';
import FieldGroup from '../../field-group';
import BaseForm from '../base-form';
import {
  ItemSyncRuleMap,
  HeadquartersModifyFieldsMap,
  ItemSyncRangeOptions,
  AutoPublishSyncOptions,
  StockSyncThresholdOptions,
  HELP_DESC_CONFIG,
} from './constants';
import { EStockSyncThreshold, EItemSyncRange } from './types';
import {
  validateFormData,
  formatFormDataForSubmit,
  extractFormInitialValues,
  shouldShowStockSync,
} from './utils';
import './index.scss';

const ShopSettingForm = forwardRef(({ yz }, ref) => {
  const [loading, setLoading] = useState(true);
  const [shopRole, setShopRole] = useState(false);
  const [isAutoUpdating, setIsAutoUpdating] = useState(false);
  const [itemSyncRangeValue, setItemSyncRangeValue] = useState('');
  const [salesInfoValue, setSalesInfoValue] = useState([]);
  const [baseInfoValue, setBaseInfoValue] = useState([]);
  const [priceInfoValue, setPriceInfoValue] = useState([]);

  const handleSubmit = useCallback((values) => {
     // 格式化数据
    const params = formatFormDataForSubmit(values);


    // 验证表单数据
    const validation = validateFormData(values);
    if (!validation.isValid) {
      validation.errors.forEach(error => {
        Notify.error(error);
      });
      return Promise.reject(new Error('表单验证失败'));
    }

    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/updateWmShopHostConfig',
        useBizRequest: true,
        method: 'post',
        data: { channelAiConfigDetailDTO: params },
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        Notify.success('保存成功');
      })
      .catch((err) => {
        console.error('保存失败:', err);
        Notify.error('保存失败');
      });
  }, [yz]);

  // 处理表单联动副作用
  useEffect(() => {
    if (isAutoUpdating || !itemSyncRangeValue) return; // 避免自动更新时的循环和初始化时的触发
    if (itemSyncRangeValue !== EItemSyncRange.all) return;

    if (!baseInfoValue.length && !priceInfoValue.length) {
      ref.current?.form.patchValue({
        itemSyncRange: EItemSyncRange.partial,
      })
      setItemSyncRangeValue(EItemSyncRange.partial);
      setTimeout(() => setIsAutoUpdating(false), 100); // 延迟重置标志位
    }
  }, [baseInfoValue, priceInfoValue]);

  useEffect(() => {
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/queryWmShopHostConfig',
      useBizRequest: true,
      method: 'get',
    })
      .then((res) => {
        const form = ref.current?.form;
        const { channelAiConfigDetailDTO } = res.data.data;

        if (form) {
          // 使用工具函数提取表单初始值
          const formData = extractFormInitialValues(channelAiConfigDetailDTO);

          setItemSyncRangeValue(formData.itemSyncRange);
          setSalesInfoValue(formData.salesInfo || []);
          setBaseInfoValue(formData.baseInfo || []);
          setPriceInfoValue(formData.priceInfo || []);

          // 初始化表单
          form.initialize(formData);

          // 设置初始表单值和店铺角色状态
          setShopRole(formData.shopRole);
        }
      })
      .catch((err) => {
        console.error('获取店铺设置失败:', err);
        Notify.error('获取店铺设置失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [yz]);

  const showSalesInfo = useMemo(() => {
    return itemSyncRangeValue === EItemSyncRange.all || itemSyncRangeValue === EItemSyncRange.partial;
  }, [itemSyncRangeValue]);
  const showBaseInfo = useMemo(() => {
    return itemSyncRangeValue === EItemSyncRange.all;
  }, [itemSyncRangeValue]);

    // 计算库存同步规则是否显示
  const showStockRealTimeSync = useMemo(() => {
    return shouldShowStockSync(salesInfoValue, showSalesInfo);
  }, [salesInfoValue, showSalesInfo]);

  return (
    <BaseForm loading={loading} ref={ref} onSubmit={handleSubmit} className="shop-setting-form">
      <FieldGroup title="店铺">
        <FormRadioGroupField
          name="operateStatus"
          label="店铺经营状态："
          required
          helpDesc="设置外部渠道的店铺经营状态，保存后系统会自动更改营业状态。"
        >
          <Radio value={1}>营业</Radio>
          <Radio value={0}>休息</Radio>
        </FormRadioGroupField>

        <FormRadioGroupField
          name="syncBusinessHours"
          label="营业时间同步："
          required
          helpDesc={
            <>
              将有赞店铺已有营业时间同步至外部店铺
              {shopRole && (
                <>
                  ，可在"
                  <a
                    onClick={() => {
                      yz.navigateTo({
                        route:
                          'https://store.youzan.com/v2/retailshop/org-manage',
                        blank: true,
                      });
                    }}
                  >
                    店铺-组织机构
                  </a>
                  "中设置有赞店铺营业时间。
                </>
              )}
              <br />
              支持每天重复的营业时间同步，每周重复设置暂不支持。
            </>
          }
        >
          <Radio value={1}>同步</Radio>
          <Radio value={0}>不同步</Radio>
        </FormRadioGroupField>
      </FieldGroup>

      <FieldGroup title="商品同步规则">
        {/* 1. 更新范围 */}
        <FormRadioGroupField
          name="itemSyncRange"
          label="更新范围："
          required
          helpDesc={HELP_DESC_CONFIG.itemSyncRange}
          onChange={(value) => {
            setItemSyncRangeValue(value);
          }}
        >
          {ItemSyncRangeOptions.map((option) => (
            <Radio key={option.value} value={option.value}>
              {option.label}
            </Radio>
          ))}
        </FormRadioGroupField>

        {/* 2. 销售信息 */}
        {showSalesInfo && (
          <FormCheckboxGroupField
            name="salesInfo"
            label="销售信息："
            className="shop-setting-checkbox-group-field"
            helpDesc={HELP_DESC_CONFIG.salesInfo}
            onChange={(values) => {
              setSalesInfoValue(values);
            }}
          >
            {ItemSyncRuleMap.find(group => group.group === 'salesInfo')?.fields.map((field) => (
              <Checkbox key={field.value} value={field.value}>
                {field.label}
              </Checkbox>
            ))}
          </FormCheckboxGroupField>
        )}

        {/* 3. 基础信息 */}
        {showBaseInfo && (
          <FormCheckboxGroupField
            name="baseInfo"
            label="基础信息："
            className="shop-setting-checkbox-group-field"
            onChange={(values) => {
              setBaseInfoValue(values);
            }}
          >
            {ItemSyncRuleMap.find(group => group.group === 'baseInfo')?.fields.map((field) => (
              <Checkbox key={field.value} value={field.value}>
                {field.label}
              </Checkbox>
            ))}
          </FormCheckboxGroupField>
        )}

        {/* 4. 价格信息 */}
        {showBaseInfo && (
          <FormCheckboxGroupField
            name="priceInfo"
            label="价格信息："
            className="shop-setting-checkbox-group-field"
            onChange={(values) => {
              setPriceInfoValue(values);
            }}
          >
            {ItemSyncRuleMap.find(group => group.group === 'priceInfo')?.fields.map((field) => (
              <Checkbox key={field.value} value={field.value}>
                {field.label}
              </Checkbox>
            ))}
          </FormCheckboxGroupField>
        )}

        {/* 5. 库存同步规则 */}
        {showStockRealTimeSync && (
          <FormRadioGroupField
            name="stockRealTimeSync"
            label="库存同步规则："
            required
            defaultValue={EStockSyncThreshold.low}
            helpDesc={HELP_DESC_CONFIG.stockRealTimeSync}
          >
            {StockSyncThresholdOptions.map((option) => (
              <Radio key={option.value} value={option.value}>
                {option.label}
                <div className="stockRealTimeSync-tip">
                  {option.tip}
                </div>
              </Radio>
            ))}
          </FormRadioGroupField>
        )}

        {/* 6. 商品库发布外卖商品时 */}
        {!!itemSyncRangeValue && <FormRadioGroupField
          name="itemAutoPublishSync"
          label="商品库发布外卖商品时："
          required
        >
          {AutoPublishSyncOptions.map((option) => (
            <Radio key={option.value} value={option.value}>
              {option.label}
            </Radio>
          ))}
        </FormRadioGroupField>}

        {/* 7. 总部可修改分店外卖商品 */}
        <FormCheckboxGroupField
          name="hqTemplateInfoSync"
          label="总部可修改分店外卖商品："
          className="shop-setting-checkbox-group-field"
          helpDesc={HELP_DESC_CONFIG.hqTemplateInfoSync}
          required
        >
          {HeadquartersModifyFieldsMap.fields.map((field) => (
            <Checkbox key={field.value} value={field.value}>
              {field.label}
            </Checkbox>
          ))}
        </FormCheckboxGroupField>
      </FieldGroup>

      <FieldGroup title="外卖开票设置">
        <FormRadioGroupField
          name="allowOpenInvoice"
          label="扫小票二维码开票："
          required
          helpDesc={`开启后，可通过扫外卖订单小票"开票二维码"开具发票。请务必前里准定有效商品和外卖渠道通商品，否则无法开票。`}
        >
          <Radio value={true}>开启</Radio>
          <Radio value={false}>关闭</Radio>
        </FormRadioGroupField>
      </FieldGroup>
    </BaseForm>
  );
});

export default ShopSettingForm;
