import React, { forwardRef, useEffect, useState } from 'react';
import {
  Checkbox,
  FormCheckboxGroupField,
  FormRadioGroupField,
  Notify,
  Radio,
} from 'zent';
import FieldGroup from '../../field-group';
import BaseForm from '../base-form';
import { AllFieldsMap, SyncFieldMap } from './constants';
import './index.scss';

const ShopSettingForm = forwardRef(({ yz }, ref) => {
  const [loading, setLoading] = useState(true);
  const [shopRole, setShopRole] = useState(false);

  const handleSubmit = (values) => {
    const {
      allowOpenInvoice,
      stockThreshold,
      syncBusinessHours,
      operateStatus,
    } = values;

    // 初始化空对象
    const outItemInfoSync = {};

    // 使用 AllFieldsMap 处理表单数据
    Object.keys(AllFieldsMap).forEach((groupName) => {
      const fieldNames = AllFieldsMap[groupName];
      const selectedValues = values[groupName] || [];

      fieldNames.forEach((fieldName) => {
        outItemInfoSync[fieldName] = selectedValues.includes(fieldName) ? 1 : 0;
      });
    });

    const params = {
      allowOpenInvoice,
      operateStatus,
      stockThreshold,
      syncBusinessHours,
      outItemInfoSync: JSON.stringify(outItemInfoSync),
    };

    return yz
      .request({
        url: '/v4/third-plugin/waimaitong-agent/updateWmShopHostConfig',
        useBizRequest: true,
        method: 'post',
        data: { channelAiConfigDetailDTO: params },
        header: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        Notify.success('保存成功');
      })
      .catch(() => {
        Notify.error('保存失败');
      });
  };

  // 从接口返回的数据中提取字段值
  const getSyncInfo = (outItemInfoSync, fields = []) => {
    return fields.filter((field) => outItemInfoSync[field]);
  };

  useEffect(() => {
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/queryWmShopHostConfig',
      useBizRequest: true,
      method: 'get',
    })
      .then((res) => {
        const form = ref.current?.form;
        const { channelAiConfigDetailDTO } = res.data.data;
        const {
          operateStatus,
          syncBusinessHours,
          allowOpenInvoice,
          stockThreshold,
          shopRole,
          outItemInfoSync: outItemInfoSyncStr,
        } = channelAiConfigDetailDTO;

        let outItemInfoSync = {};
        try {
          outItemInfoSync = JSON.parse(outItemInfoSyncStr);
        } catch (err) {
          console.log('err', err);
        }

        if (form) {
          // 构造初始化表单数据
          const formData = {
            operateStatus,
            syncBusinessHours,
            allowOpenInvoice,
            stockThreshold: stockThreshold <= 15 ? 15 : 50,
          };

          // 使用 AllFieldsMap 提取已选中的字段
          Object.keys(AllFieldsMap).forEach((groupName) => {
            formData[groupName] = getSyncInfo(
              outItemInfoSync,
              AllFieldsMap[groupName]
            );
          });

          // 初始化表单
          form.initialize(formData);
        }
        setShopRole(Boolean(shopRole));
      })
      .catch((err) => {
        console.log('err', err);
        Notify.error('获取店铺设置失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <BaseForm loading={loading} ref={ref} onSubmit={handleSubmit}>
      <FieldGroup title="店铺">
        <FormRadioGroupField
          name="operateStatus"
          label="店铺经营状态："
          required
          helpDesc="设置外部渠道的店铺经营状态，保存后系统会自动更改营业状态。"
        >
          <Radio value={1}>营业</Radio>
          <Radio value={0}>休息</Radio>
        </FormRadioGroupField>

        <FormRadioGroupField
          name="syncBusinessHours"
          label="营业时间同步："
          required
          helpDesc={
            <>
              将有赞店铺已有营业时间同步至外部店铺
              {shopRole && (
                <>
                  ，可在"
                  <a
                    onClick={() => {
                      yz.navigateTo({
                        route:
                          'https://store.youzan.com/v2/retailshop/org-manage',
                        blank: true,
                      });
                    }}
                  >
                    店铺-组织机构
                  </a>
                  "中设置有赞店铺营业时间。
                </>
              )}
              <br />
              支持每天重复的营业时间同步，每周重复设置暂不支持。
            </>
          }
        >
          <Radio value={1}>同步</Radio>
          <Radio value={0}>不同步</Radio>
        </FormRadioGroupField>
      </FieldGroup>

      <FieldGroup title="库存同步规则">
        <FormRadioGroupField
          name="stockThreshold"
          label="同步时效要求："
          required
          helpDesc="库存敏感度高（如面包/蛋糕/商超等商家）建议超高要求，日库存充足（如餐饮/咖啡茶饮等商家）选择中等要求"
        >
          <Radio value={50}>超高要求</Radio>
          <Radio value={15}>中等要求</Radio>
        </FormRadioGroupField>
      </FieldGroup>

      <FieldGroup title="同步外卖商品字段">
        {SyncFieldMap.map((field) => (
          <FormCheckboxGroupField
            key={field.group}
            name={field.group}
            label={`${field.groupLabel}：`}
            className="shop-setting-checkbox-group-field"
          >
            {field.fields.map((field) => (
              <Checkbox
                style={{ visibility: field.hidden ? 'hidden' : 'visible' }}
                key={field.name}
                value={field.name}
              >
                {field.label}
              </Checkbox>
            ))}
          </FormCheckboxGroupField>
        ))}
      </FieldGroup>

      <FieldGroup title="外卖开票设置">
        <FormRadioGroupField
          name="allowOpenInvoice"
          label="扫小票二维码开票："
          required
          helpDesc={`开启后，可通过扫外卖订单小票"开票二维码"开具发票。请务必前里准定有效商品和外卖渠道通商品，否则无法开票。`}
        >
          <Radio value={true}>开启</Radio>
          <Radio value={false}>关闭</Radio>
        </FormRadioGroupField>
      </FieldGroup>
    </BaseForm>
  );
});

export default ShopSettingForm;
