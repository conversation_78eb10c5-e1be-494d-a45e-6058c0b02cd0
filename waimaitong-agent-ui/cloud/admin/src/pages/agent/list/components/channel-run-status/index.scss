.list-desc {
  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    line-height: 24px;
    margin-bottom: 8px;
  }

  &-content {
    font-size: 14px;
    color: #999;
    line-height: 22px;
  }

  &-divider {
    width: 100%;
    height: 1px;
    background-color: #eee;
    margin: 20px 0;
  }

  &-channel {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    &-item {
      display: flex;
      flex-direction: column;
      gap: 5px;
      img {
        display: block;
        width: 60px;
        height: 60px;
        border-radius: 6px;
      }
      span {
        font-size: 14px;
        padding-left: 14px;
        position: relative;
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: #999;
        }
      }
      &.active {
        span {
          color: #45a110;
          &:before {
            background-color: #45a110;
          }
        }
      }
    }
  }
}