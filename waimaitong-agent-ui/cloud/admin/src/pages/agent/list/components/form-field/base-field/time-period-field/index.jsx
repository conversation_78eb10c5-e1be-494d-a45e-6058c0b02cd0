import React, { useCallback } from 'react';
import {
  FieldSet,
  Form,
  FormError,
  FormNumberInputField,
  FormTimeRangePickerField,
  Icon,
  ValidateOption,
  Validators,
} from 'zent';
import './index.scss';

function TimePeriodItem({ index, onRemove, afterTimeLabel, model }) {
  return (
    <div className="time-period-item">
      <div className="time-period-label">
        时段
        {index + 1}：
      </div>
      <div className="time-period-content">
        <div className="time-period-content-item">
          <FormTimeRangePickerField
            name="timeRange"
            props={{
              showSecond: false,
              format: 'HH:mm',
              width: 120,
            }}
            validators={[
              (value) => {
                if (!value[0] || !value[1]) {
                  return {
                    message: '请选择时间',
                    name: 'timeRange',
                  };
                }
              },
            ]}
            withoutLabel
            className="time-period-picker"
          />
          <span>，{afterTimeLabel}</span>
          <FormNumberInputField
            name="delay"
            props={{
              min: 0,
              max: 120,
              width: 140,
              addonAfter: '分钟',
            }}
            validators={[Validators.required('请输入时间')]}
            withoutLabel
            className="time-period-input"
          />
          <span>，自动出餐</span>
        </div>
        <Icon
          type="subtract-circle-o"
          className="time-period-remove"
          onClick={() => onRemove(index)}
        />
      </div>
    </div>
  );
}

function TimePeriodField({
  name,
  afterTimeLabel = '接单后',
  maxLength = Infinity,
}) {
  const fieldArrayModel = Form.useFieldArray(name, [
    Validators.minLength(1, '至少需要一个时段'),
    // 简化的时间段重叠检查
    (value) => {
      // 先把无效的时间段过滤掉
      const validPeriods = value.filter(
        (period) =>
          period.timeRange && period.timeRange[0] && period.timeRange[1]
      );

      if (validPeriods.length <= 1) return null;

      // 将时间字符串转为分钟数
      const toMinutes = (time) => {
        const [h, m] = time.split(':').map(Number);
        return h * 60 + m;
      };

      // 检查重叠
      for (let i = 0; i < validPeriods.length - 1; i++) {
        const [start1, end1] = validPeriods[i].timeRange.map(toMinutes);

        for (let j = i + 1; j < validPeriods.length; j++) {
          const [start2, end2] = validPeriods[j].timeRange.map(toMinutes);

          // 检查时间段是否重叠
          if (start1 < end2 && start2 < end1) {
            return {
              message: '时间段不能重叠',
              name: 'timeRange',
            };
          }
        }
      }

      return null;
    },
  ]);

  const addPeriod = useCallback(() => {
    fieldArrayModel.push({
      timeRange: ['', ''],
      delay: 10,
    });
    fieldArrayModel.validate(ValidateOption.IncludeUntouched);
  }, [fieldArrayModel]);

  const removePeriod = useCallback(
    (index) => {
      fieldArrayModel.splice(index, 1);
      fieldArrayModel.validate();
    },
    [fieldArrayModel]
  );

  return (
    <>
      <div className="time-period-container">
        <div className="time-periods-wrapper">
          {fieldArrayModel.children.map((child, index) => (
            <FieldSet key={child.id} model={child}>
              <TimePeriodItem
                model={child}
                index={index}
                onRemove={removePeriod}
                afterTimeLabel={afterTimeLabel}
              />
            </FieldSet>
          ))}
        </div>
        {fieldArrayModel.children.length < maxLength && (
          <span className="add-time-period-btn" onClick={addPeriod}>
            <Icon type="plus-circle-o" />
            添加时段
          </span>
        )}
      </div>
      <FormError>{fieldArrayModel.error?.message}</FormError>
    </>
  );
}

export default TimePeriodField;
