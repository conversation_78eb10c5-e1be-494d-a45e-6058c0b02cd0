import cx from 'classnames';
import React, { useMemo } from 'react';
import { Notify } from 'zent';
import { SkillTemplateIdMap } from '../../constants';
import {
  GoodsDiscardDrawer,
  GoodsOffShelfDrawer,
  GoodsSoldoutDrawer,
  GoodsSoldoutReminderDrawer,
  GoodsUnbindDrawer,
  OrderAftersaleDrawer,
  OrderDiningoutDrawer,
  OrderPickupStatusDrawer,
  OrderTimeoutDrawer,
  ShopSettingDrawer,
} from '../form-field';
import './index.scss';

// SkillItem组件 - 单个技能项
const SkillItem = ({
  skill,
  yz,
  refreshSkill,
  handleEnableSkill,
  itemSelectorRef,
  agentId,
}) => {
  const { enable, skillTemplateId, name, description, id, config } = skill;

  const editSkill = useMemo(() => {
    switch (skillTemplateId) {
      case SkillTemplateIdMap.ShopSetting:
        return (
          <ShopSettingDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.GoodsSoldout:
        return (
          <GoodsSoldoutDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.GoodsSoldoutReminder:
        return (
          <GoodsSoldoutReminderDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.GoodsOffShelf:
        return (
          <GoodsOffShelfDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.GoodsDiscard:
        return (
          <GoodsDiscardDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.GoodsUnbind:
        return (
          <GoodsUnbindDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.OrderDiningout:
        return (
          <OrderDiningoutDrawer
            yz={yz}
            skill={skill}
            itemSelectorRef={itemSelectorRef}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.OrderAftersale:
        return (
          <OrderAftersaleDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.OrderTimeout:
        return (
          <OrderTimeoutDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      case SkillTemplateIdMap.OrderPickupStatus:
        return (
          <OrderPickupStatusDrawer
            yz={yz}
            skill={skill}
            agentId={agentId}
            refreshSkill={refreshSkill}
          />
        );
      default:
        return null;
    }
  }, [skillTemplateId]);

  const handleRunSkill = () => {
    if (skillTemplateId === SkillTemplateIdMap.ShopSetting) {
      yz.request({
        url: '/v4/third-plugin/waimaitong-agent/updateWmShopHostConfig',
        useBizRequest: true,
        method: 'post',
        data: { enable: !enable },
        header: {
          'Content-Type': 'application/json',
        },
      })
        .then(() => {
          Notify.success(`${!enable ? '启用' : '关闭'}成功`);
          refreshSkill();
          handleEnableSkill(!enable);
        })
        .catch((error) => {
          console.log('error', error);
          Notify.error(`${!enable ? '启用' : '关闭'}失败`);
        });
      return;
    }

    if (!config.flowRule) {
      Notify.error('请先完善技能配置');
      return;
    }

    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/saveSkill',
      useBizRequest: true,
      method: 'post',
      data: {
        agentId,
        skillId: id,
        enable: !enable,
        config,
      },
      header: {
        'Content-Type': 'application/json',
      },
    })
      .then((res) => {
        if (!res.data) {
          return Promise.reject();
        }
        Notify.success(`${!enable ? '启用' : '关闭'}成功`);
        refreshSkill();
        handleEnableSkill(!enable);
      })
      .catch((error) => {
        console.log('error', error);
        Notify.error(`${!enable ? '启用' : '关闭'}失败`);
      });
  };

  const runSkill = useMemo(() => {
    return (
      <img
        className="skill-item-run"
        onClick={handleRunSkill}
        src={
          enable
            ? 'https://img01.yzcdn.cn/upload_files/2025/03/21/FtJUjeEsHXlGjn1ozzi_er1JHCU-.png'
            : 'https://img01.yzcdn.cn/upload_files/2025/03/21/FvOPRgn4Y4XWUf7IdqWCuCsbJuXw.png'
        }
      />
    );
  }, [skill]);

  return (
    <div className="skill-item">
      <div className="skill-item-content">
        <div className="skill-item-info">
          <div className="skill-item-name">{name}</div>
          <span
            className={cx('skill-item-status', {
              running: enable,
            })}
          >
            {enable ? '运行中' : '待启用'}
          </span>
        </div>
        {description && <div className="skill-item-desc">{description}</div>}
      </div>
      <div className="skill-item-actions">
        {editSkill}
        {runSkill}
      </div>
    </div>
  );
};

// 主组件，负责渲染SkillHeader和SkillItem列表
const SkillList = ({
  header,
  skills,
  yz,
  refreshSkill,
  handleEnableSkill,
  itemSelectorRef,
  agentId,
}) => {
  return (
    <div className="skill-list">
      {header && (
        <div className="skill-header">
          <div className="skill-header-title">{header}</div>
        </div>
      )}

      <div className={cx('skill-items', { 'no-header': !header })}>
        {skills &&
          skills.map((skill, index) => (
            <SkillItem
              key={skill.skillTemplateId || index}
              skill={skill}
              yz={yz}
              refreshSkill={refreshSkill}
              handleEnableSkill={handleEnableSkill}
              itemSelectorRef={itemSelectorRef}
              agentId={agentId}
            />
          ))}
      </div>
    </div>
  );
};

export default SkillList;
