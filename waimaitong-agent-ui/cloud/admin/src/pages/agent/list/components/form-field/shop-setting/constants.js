import { EItemSyncRange, EAutoPublishSync, EStockSyncThreshold, FIELD_OPTIONS } from './types';

// 商品同步规则配置映射
export const ItemSyncRuleMap = [
  {
    group: 'salesInfo',
    groupLabel: '销售信息',
    fields: FIELD_OPTIONS.salesInfo,
  },
  {
    group: 'baseInfo',
    groupLabel: '基础信息',
    fields: FIELD_OPTIONS.baseInfo,
  },
  {
    group: 'priceInfo',
    groupLabel: '价格信息',
    fields: FIELD_OPTIONS.priceInfo,
  },
];

// 总部可修改分店外卖商品字段映射
export const HeadquartersModifyFieldsMap = {
  group: 'hqTemplateInfoSync',
  groupLabel: '总部可修改分店外卖商品',
  fields: FIELD_OPTIONS.hqTemplateInfoSync,
};

// 更新范围选项
export const ItemSyncRangeOptions = [
  { value: EItemSyncRange.partial, label: '指定部分信息' },
  { value: EItemSyncRange.all, label: '全部信息' },
  { value: EItemSyncRange.bindOnly, label: '仅绑定商品，不更新信息' },
];

// 字段可见性映射 - 根据更新范围控制字段显示
export const FIELD_VISIBILITY_MAP = {
  [EItemSyncRange.all]: {
    salesInfo: true,
    baseInfo: true,
    priceInfo: true,
    itemAutoPublishSync: true,
    hqTemplateInfoSync: true,
  },
  [EItemSyncRange.partial]: {
    salesInfo: true,
    baseInfo: false,
    priceInfo: false,
    itemAutoPublishSync: true,
    hqTemplateInfoSync: true,
  },
  [EItemSyncRange.bindOnly]: {
    salesInfo: false,
    baseInfo: false,
    priceInfo: false,
    itemAutoPublishSync: true,
    hqTemplateInfoSync: true,
  },
};

// 帮助文本配置
export const HELP_DESC_CONFIG = {
  itemSyncRange: '更新范围仅对已经绑定的外卖平台商品生效',
  salesInfo: '仅饿了么外卖和京东外卖渠道，支持同步销售状态',
  stockRealTimeSync: '库存敏感度高（如面包/蛋糕/商超等商家）建议超高要求，日库存充足（如餐饮/咖啡茶饮等商家）选择中等要求',
  hqTemplateInfoSync: '默认总部强管控,商品库更新将覆盖分店外卖渠道商品信息非总部强管控可取消勾选。',
};

// 商品库发布外卖商品时选项
export const AutoPublishSyncOptions = [
  { value: EAutoPublishSync.auto, label: '自动发布创建到外卖平台' },
  { value: EAutoPublishSync.manual, label: '手动同步创建到外卖平台' },
];

// 库存同步规则选项
export const StockSyncThresholdOptions = [
  {
    value: EStockSyncThreshold.low,
    label: '当商品增加库存，或可售库存≤15个时，实时同步',
    tip: '适合日库存固定/有限商家（如面包/商超便利等业态）',
  },
  {
    value: EStockSyncThreshold.high,
    label: '当商品增加库存，或可售库存≤50个时，实时同步',
    tip: '适合日库存充足/无限商家（如正餐西餐、咖啡茶饮、仅生日蛋糕等业态）',
  },
];

// 所有字段映射（用于数据处理）
export const AllFieldsMap = ItemSyncRuleMap.reduce((result, group) => {
  result[group.group] = group.fields.map((field) => field.value);
  return result;
}, {});

// 添加总部可修改字段
AllFieldsMap[HeadquartersModifyFieldsMap.group] = HeadquartersModifyFieldsMap.fields.map((field) => field.value);

// 向后兼容的旧版字段映射（保留以支持现有数据）
export const LegacySyncFieldMap = [
  {
    group: 'stockInfo',
    groupLabel: '库存数量',
    fields: [
      {
        name: 'stockNum',
        label: '商品库存',
      },
    ],
  },
  {
    group: 'baseInfo',
    groupLabel: '基础信息',
    fields: [
      {
        name: 'title',
        label: '商品/规格名称',
      },
      {
        name: 'category',
        label: '商品类目',
      },
      {
        name: 'prop',
        label: '商品属性',
      },
      {
        name: 'picture',
        label: '商品主图',
      },
      {
        name: 'group',
        label: '商品分组',
      },
      {
        name: 'sellPoint',
        label: '商品描述',
      },
      {
        name: 'groupSequence',
        label: '分组排序',
        hidden: true,
      },
    ],
  },
  {
    group: 'priceInfo',
    groupLabel: '价格信息',
    fields: [
      {
        name: 'price',
        label: '商品价格',
      },
      {
        name: 'packingCharge',
        label: '打包费',
      },
    ],
  },
  {
    group: 'otherInfo',
    groupLabel: '其他信息',
    fields: [
      {
        name: 'minimumPurchaseNum',
        label: '最小起购量',
      },
    ],
  },
];
