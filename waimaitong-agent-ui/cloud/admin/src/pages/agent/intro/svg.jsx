export const FusionSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_4029_26426)">
      <path
        d="M12.0504 7.53379C14.0496 7.53379 15.6704 5.89219 15.6704 3.867C15.6704 1.8418 14.0496 0.200195 12.0504 0.200195C10.0512 0.200195 8.43039 1.8418 8.43039 3.867C8.43039 4.8394 8.81199 5.77219 9.49079 6.45979C10.1696 7.14739 11.0904 7.53379 12.0504 7.53379ZM12.0504 1.7978C13.1928 1.7978 14.1192 2.7358 14.1192 3.893C14.1192 5.0502 13.1932 5.9882 12.0504 5.9882C10.908 5.9882 9.98199 5.0502 9.98199 3.893C9.98199 3.3374 10.2 2.8042 10.588 2.4114C10.9756 2.0186 11.502 1.7978 12.0504 1.7978ZM7.13759 16.1378C6.27639 14.6206 4.47639 13.9314 2.83959 14.4922C1.20279 15.053 0.185589 16.7074 0.410389 18.443C0.635189 20.1782 2.03959 21.5106 3.76399 21.625C5.48839 21.739 7.05159 20.6026 7.49719 18.9118C7.74799 17.9754 7.61839 16.9766 7.13759 16.1378ZM5.99959 18.495C5.72479 19.5382 4.70839 20.1954 3.65839 20.009C2.60839 19.8226 1.87359 18.8542 1.96519 17.779C2.05679 16.7034 2.94519 15.877 4.01079 15.8762C4.19159 15.8758 4.37159 15.8998 4.54599 15.947C5.63839 16.251 6.28599 17.3882 5.99919 18.4982V18.495H5.99959ZM23.5624 17.0178C23.116 15.3346 21.558 14.205 19.8412 14.3198C18.124 14.4346 16.7264 15.7618 16.5024 17.4902C16.2784 19.2186 17.2908 20.8662 18.9204 21.4258C20.55 21.9854 22.3432 21.3014 23.2032 19.7918C23.684 18.9526 23.8132 17.9538 23.5624 17.0178ZM21.8584 19.0054C21.368 19.875 20.3388 20.2718 19.4016 19.9522C18.4644 19.6326 17.882 18.6858 18.0108 17.693C18.14 16.7002 18.9448 15.9386 19.9316 15.8754C20.9184 15.8122 21.8112 16.4654 22.0624 17.4338C22.2004 17.965 22.1272 18.529 21.8584 19.0054ZM17.0616 6.22939C19.0528 7.68019 20.3348 9.92579 20.5836 12.3974C20.6236 12.8002 20.9596 13.1066 21.3592 13.1046H21.4368C21.8632 13.0618 22.1744 12.6774 22.1324 12.2454C21.8424 9.33219 20.3368 6.68339 17.9952 4.96699C17.8858 4.87957 17.7546 4.8237 17.6158 4.80544C17.477 4.78718 17.3358 4.80722 17.2076 4.86339C16.9464 4.97779 16.768 5.22819 16.7428 5.51539C16.7176 5.80259 16.85 6.0806 17.0876 6.23979L17.0616 6.22939ZM15.562 21.1874C13.328 22.205 10.7704 22.205 8.53639 21.1874C8.16279 21.0754 7.76479 21.2618 7.60719 21.6226C7.44919 21.9834 7.58039 22.407 7.91359 22.6122C10.5456 23.8066 13.556 23.8066 16.188 22.6122C16.5664 22.427 16.7304 21.9706 16.558 21.5826C16.3856 21.1946 15.9396 21.0158 15.552 21.1798L15.562 21.1874ZM2.67439 13.0942H2.75199C3.14439 13.0914 3.47279 12.7922 3.51759 12.3974C3.76599 9.92579 5.04839 7.68019 7.03959 6.22939C7.27679 6.0702 7.40919 5.79219 7.38399 5.50499C7.35879 5.21779 7.18039 4.96739 6.91919 4.85299C6.79101 4.79682 6.6499 4.77678 6.51114 4.79504C6.37239 4.8133 6.24127 4.86917 6.13199 4.95659C3.78999 6.67339 2.28479 9.32179 1.99439 12.235C1.95199 12.6614 2.25439 13.0434 2.67439 13.0942Z"
        fill="#333333"
      />
    </g>
    <defs>
      <clipPath id="clip0_4029_26426">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const GoodsSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.84247 7.19981C7.8425 7.20005 7.84254 7.2003 7.84257 7.20055H16.1572C16.1572 7.2003 16.1573 7.20005 16.1573 7.19981H17.4957L18.4957 19.1998H5.50403L6.50403 7.19981H7.84247ZM11.9999 2.39941C14.1159 2.39941 15.8664 3.96424 16.1574 5.9998H17.4958C18.1199 5.9998 18.6398 6.4782 18.6916 7.10015L19.6916 19.1002C19.7467 19.7606 19.2559 20.3406 18.5954 20.3957L18.4958 20.3998H5.50408C4.84134 20.3998 4.30408 19.8625 4.30408 19.1998L4.30822 19.1002L5.30822 7.10015C5.36005 6.4782 5.87997 5.9998 6.50408 5.9998H7.84236C8.13333 3.96424 9.88386 2.39941 11.9999 2.39941ZM11.9998 3.59941C10.6081 3.59941 9.43766 4.54714 9.09876 5.83243L9.05862 5.99941H14.9398L14.9009 5.83243C14.5781 4.60834 13.5012 3.69044 12.1971 3.6058L11.9998 3.59941Z"
      fill="#333333"
    />
  </svg>
);

export const OrderSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M17.9999 2.39941C18.6627 2.39941 19.1999 2.93667 19.1999 3.59941V20.3994C19.1999 21.0622 18.6627 21.5994 17.9999 21.5994H5.99993C5.33719 21.5994 4.79993 21.0622 4.79993 20.3994V3.59941C4.79993 2.93667 5.33719 2.39941 5.99993 2.39941H17.9999ZM17.9999 3.59941H5.99993V20.3994H17.9999V3.59941ZM15.6 7.1998H8.39998V8.39981H15.6V7.1998ZM8.39998 10.7998H15.6V11.9998H8.39998V10.7998ZM13.2 14.3998H8.39998V15.5998H13.2V14.3998Z"
      fill="#333333"
    />
  </svg>
);

export const EfficiencySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.2887 2.02686C18.1085 2.43381 22.5128 7.35967 22.1157 13.0382C22.0887 13.4244 21.7537 13.7156 21.3674 13.6886C20.9866 13.6619 20.6982 13.3361 20.7159 12.9569L20.7169 12.9404C21.0593 8.04304 17.2467 3.77893 12.1909 3.4254C7.13515 3.07187 2.76621 6.76384 2.42374 11.6612C2.08132 16.5585 5.8939 20.8227 10.9497 21.1762C11.7384 21.2313 12.5201 21.1888 13.2818 21.052C13.6629 20.9836 14.0274 21.2369 14.0959 21.618C14.1644 21.999 13.9109 22.3634 13.5298 22.4319C12.6543 22.5891 11.7564 22.638 10.8519 22.5747C5.03205 22.1678 0.627796 17.2419 1.02487 11.5634C1.42195 5.88484 6.46889 1.61989 12.2887 2.02686H12.2887ZM13.3498 13.1488L21.7359 16.1294C22.0174 16.234 22.158 16.5302 22.0525 16.7916C22.0046 16.9209 21.9065 17.026 21.7802 17.0839L18.589 18.4998C18.3435 18.6037 18.1482 18.7973 18.044 19.04L16.6901 22.2296C16.5858 22.4884 16.2745 22.6107 16.0124 22.5116L16.0045 22.5086C15.8639 22.4563 15.7584 22.3342 15.7057 22.1948L12.6816 13.8111C12.6575 13.7472 12.6465 13.6791 12.6494 13.6109C12.6524 13.5426 12.6691 13.4757 12.6985 13.4141C12.7581 13.2895 12.8662 13.1938 12.9982 13.1488C13.1036 13.0966 13.2267 13.0966 13.3498 13.1488ZM14.5834 15.0422L16.2684 19.7138L16.784 18.4992C17.0233 17.9419 17.4657 17.497 18.0202 17.2521L18.0393 17.2438L19.2574 16.7034L14.5834 15.0422ZM11.5703 7.12431C14.4299 7.12431 16.748 9.4419 16.748 12.3008C16.748 12.6879 16.4341 13.0018 16.0469 13.0018C15.6652 13.0018 15.3547 12.6969 15.3459 12.3173L15.3457 12.3008C15.3457 10.2162 13.6554 8.52627 11.5703 8.52627C9.48518 8.52627 7.79487 10.2162 7.79487 12.3008C7.79487 14.3646 9.45155 16.0415 11.5079 16.0748L11.5703 16.0753C11.9575 16.0753 12.2715 16.3891 12.2715 16.7763C12.2715 17.1634 11.9575 17.4773 11.5703 17.4773C8.71071 17.4773 6.39258 15.1597 6.39258 12.3008C6.39258 9.4419 8.71071 7.12431 11.5703 7.12431Z"
      fill="#333333"
    />
  </svg>
);
