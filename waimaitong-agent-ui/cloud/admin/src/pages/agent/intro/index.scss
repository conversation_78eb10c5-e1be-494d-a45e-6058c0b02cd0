.intro-wrapper {
  margin: -16px;
  background-color: #f7f8fa;

  .container {
    width: 964px;
    background-image: url('https://img01.yzcdn.cn/upload_files/2025/03/13/FqxY4PkOvYbLSV8DCCYiufpwIAS0.png');
    background-size: cover;
    background-position: center;
    padding: 103px 68px 24px;
    box-sizing: border-box;
    border-radius: 8px;
    margin: 0 auto;
  }
  
  .title {
    font-size: 28px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    line-height: 1;
  }
  
  .description {
    font-size: 16px;
    color: #666;
    line-height: 28px;
    margin-bottom: 74px;
  }
  
  .featureList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 80px;
    margin-bottom: 95px;
  }
  
  .featureRow {
    width: calc(50% - 40px);
    &:nth-child(1),
    &:nth-child(2) {
      margin-bottom: 0;
    }
  }
  
  .featureHeader {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }
  
  .featureTitle {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    line-height: 32px;
    height: 32px;
  }
  
  .featureDesc {
    font-size: 14px;
    color: #666;
    line-height: 24px;
  }
  
  .actionArea {
    display: flex;
    justify-content: center;
  }
  
  .actionButton {
    width: 288px;
  }
}