import React from 'react';
import { Button } from 'zent';
import { Stage, TestShopIds } from '../constant';
import './index.scss';
import { EfficiencySvg, FusionSvg, GoodsSvg, OrderSvg } from './svg';

class Page extends React.Component {
  render() {
    const { onChangeStage, yz: yzProps } = this.props;
    const { kdtId } = yzProps.data.shop;
    const isTestShop = TestShopIds.includes(kdtId);

    return (
      <div className="intro-wrapper">
        <div className="container">
          <div className="content">
            <h1 className="title">全网外卖通托管</h1>
            <p className="description">
              日均可减少人工介入时间 5 小时，让你的外卖生意和效率
              <br />
              更上一层楼
            </p>

            <div className="featureList">
              <div className="featureRow">
                <div className="featureItem">
                  <div className="featureHeader">
                    <FusionSvg />
                    <div className="featureTitle">多平台外卖渠道整合</div>
                  </div>
                  <p className="featureDesc">
                    一键在全外卖渠道智能托管，外卖策略100%实现统一管理
                  </p>
                </div>
              </div>

              <div className="featureRow">
                <div className="featureItem">
                  <div className="featureHeader">
                    <GoodsSvg />
                    <div className="featureTitle">一键配置</div>
                  </div>
                  <p className="featureDesc">
                    首店、新店开业...一键完成店铺初始设置，漏配、错配失误率趋近于0
                  </p>
                </div>
              </div>

              <div className="featureRow">
                <div className="featureItem">
                  <div className="featureHeader">
                    <EfficiencySvg />
                    <div className="featureTitle">商品洞察</div>
                  </div>
                  <p className="featureDesc">
                    实时感知外卖商品变化，覆盖库存售罄、异常解绑、日常汰换三大高频场景，解放您的双手和精力
                  </p>
                </div>
              </div>

              <div className="featureRow">
                <div className="featureItem">
                  <div className="featureHeader">
                    <OrderSvg />
                    <div className="featureTitle">订单处理提效</div>
                  </div>
                  <p className="featureDesc">
                    {`订单处理效率提升 50%，支持${
                      isTestShop ? '' : '智能出餐、'
                    }异常订单通知、售后自动化任务等技能`}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="actionArea">
            <Button
              className="actionButton"
              type="primary"
              size="large"
              onClick={() => {
                onChangeStage(Stage.Progress);
              }}
            >
              开启我的外卖托管
            </Button>
          </div>
        </div>
      </div>
    );
  }
}

export default Page;
