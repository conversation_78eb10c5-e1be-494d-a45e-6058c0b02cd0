import React from 'react';
import { SkillTemplateIdMap } from './constants';

export function getFormattedDate() {
  const date = new Date();

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  return `${year}年${month}月${day}日`;
}

export function isEmpty(data) {
  if (data == null) {
    return true;
  }

  if (typeof data === 'object') {
    if (Array.isArray(data)) {
      return data.length === 0;
    }
    return Object.keys(data).length === 0;
  }
  return false;
}

export function getGoodsSettingDesc(SyncFieldMap, formValue) {
  const groupStatuses = SyncFieldMap.map(group => {
    const groupKey = group.group;
    const fields = formValue[groupKey] || [];

    const requiredFields = group.fields.filter(f => !f.hidden).map(f => f.name);

    if (requiredFields.length === 0) return null;

    const matched = requiredFields.filter(name => fields.includes(name));

    if (matched.length === 0) return '-';
    if (matched.length === requiredFields.length) return 'full';
    return 'partial';
  }).filter(Boolean);

  if (groupStatuses.length === 0) return '-';

  const hasPartial = groupStatuses.includes('partial');
  const allFull = groupStatuses.every(s => s === 'full');

  return allFull ? '全部商品资料' : hasPartial ? '部分商品资料' : '-';
}

const extractCleanText = node => {
  const collectText = currentNode => {
    if (typeof currentNode === 'string') return [currentNode];
    if (typeof currentNode === 'number') return [String(currentNode)];
    if (React.isValidElement(currentNode)) {
      return collectText(currentNode.props.children);
    }
    if (Array.isArray(currentNode)) {
      return currentNode.flatMap(child => collectText(child));
    }
    return [];
  };

  return collectText(node)
    .join(' ')
    .replace(/\s+/g, ' ')
    .replace(/\([^)]*\)/g, '')
    .trim();
};

export function getSectionsDesc(list) {
  const sections = list.slice(1, 5);

  return sections.map(section => {
    const cleanTitle = section.title.replace(/^[^、]+、/, '').trim();

    const points = (section.bulletPoints || [])
      .map(bp => {
        const cleanDesc = extractCleanText(bp.desc);
        return cleanDesc ? `${bp.label}：${cleanDesc}` : null;
      })
      .filter(Boolean)
      .join('；');

    return points ? `配置${cleanTitle}：${points}` : null;
  });
}

export function filterSections(sections, formValue) {
  const formValueMap = Object.entries(formValue).reduce((acc, [key, value]) => {
    acc[parseInt(key, 10)] = value;
    return acc;
  }, {});

  return sections.map(section => {
    const newSection = { ...section };

    if (newSection.bulletPoints) {
      newSection.bulletPoints = newSection.bulletPoints.filter(bp => {
        if (bp.skillTemplateId === undefined) return true;

        const config = formValueMap[bp.skillTemplateId];

        return config?.enable !== false;
      });
    }

    return newSection;
  });
}

export function validateForm(skillTemplateId, formValue = {}) {
  const commonChannelReceiverCheck = value =>
    !isEmpty(value?.msgChannels) && !isEmpty(value?.receivers);

  const orderPeriodCheck = value =>
    !isEmpty(value.instantPeriods) && !isEmpty(value.reservationPeriods);

  const validationMap = {
    [SkillTemplateIdMap.GoodsSoldout]: commonChannelReceiverCheck,
    [SkillTemplateIdMap.GoodsUnbind]: commonChannelReceiverCheck,
    [SkillTemplateIdMap.GoodsDiscard]: commonChannelReceiverCheck,
    [SkillTemplateIdMap.OrderTimeout]: commonChannelReceiverCheck,
    [SkillTemplateIdMap.OrderPickupStatus]: commonChannelReceiverCheck,
    [SkillTemplateIdMap.OrderDiningout]: orderPeriodCheck
  };

  const templateValue = formValue[skillTemplateId] || {};
  const validator = validationMap[skillTemplateId] || (() => true);
  return validator(templateValue);
}
