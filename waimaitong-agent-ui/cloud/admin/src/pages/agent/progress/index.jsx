import React from 'react';
import { Indicator } from 'zent';
import AutoLearning from './components/auto-learning';
import Launch from './components/launch';
import PlanPreview from './components/plan-preview';
import ShopCheck from './components/shop-check';
import './index.scss';

const stepsConfig = [
  { title: '自动学习', component: AutoLearning },
  { title: '店铺检测', component: ShopCheck },
  { title: '预览方案', component: PlanPreview },
  { title: '正式启用', component: Launch },
];

class Page extends React.Component {
  formValueRef = React.createRef({});
  planListRef = React.createRef([]);

  state = {
    current: 1,
    status: 'process',
  };

  nextStep = () => {
    const { current, status } = this.state;
    const totalSteps = stepsConfig.length;

    if (current === totalSteps && status === 'process') {
      this.setState({ status: 'finish' });
    } else {
      const nextCurrent = (current % totalSteps) + 1;
      this.setState({
        current: nextCurrent,
        status: 'process',
      });
    }
  };

  render() {
    const { current, status } = this.state;
    const { agentId, yz: yzProps } = this.props;
    // const CurrentStepComponent = stepsConfig[current - 1]?.component; // 为什么报错了 - -

    return (
      <div className="page">
        <Indicator current={current} status={status} className="page__steps">
          {stepsConfig.map((step, index) => (
            <Indicator.Step key={index + 1} title={step.title} />
          ))}
        </Indicator>
        <div className="page__content">
          {/* <CurrentStepComponent ></CurrentStepComponent> */}
          {current === 1 && (
            <AutoLearning handleNextStep={this.nextStep}></AutoLearning>
          )}
          {current === 2 && (
            <ShopCheck
              handleNextStep={this.nextStep}
              agentId={agentId}
              yz={yzProps}
            ></ShopCheck>
          )}
          {current === 3 && (
            <PlanPreview
              handleNextStep={this.nextStep}
              onFormValueChange={(formValue) => {
                this.formValueRef = formValue;
              }}
              onPlanListChange={(value) => {
                this.planListRef = value;
              }}
              yz={yzProps}
            ></PlanPreview>
          )}
          {current === 4 && (
            <Launch
              handleNextStep={this.nextStep}
              formValue={this.formValueRef}
              agentId={agentId}
              planList={this.planListRef}
              handleChangeStage={this.props.onChangeStage}
            ></Launch>
          )}
        </div>
      </div>
    );
  }
}

export default Page;
