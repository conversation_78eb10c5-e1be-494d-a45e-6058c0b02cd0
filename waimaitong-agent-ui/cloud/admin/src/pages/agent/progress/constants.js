export const AgentMessageBlockTypeEnum = {
  /** 文本消息 */
  TEXT: 0,
  /** 图片消息 */
  IMAGE: 1,
  /** 按钮组消息 */
  BTN_GROUP: 2,
  /** 思维链消息 */
  THOUGHT_CHAIN: 3,
  /** 任务执行消息 */
  TASK_EXECUTION: 4,
};

export const AgentTaskExecutionStatusEnum = {
  /** 等待执行 */
  PENDING: 0,
  /** 正在执行 */
  RUNNING: 1,
  /** 执行完成 */
  COMPLETED: 2,
  /** 执行失败 */
  FAILED: 3,
};

export const SkillTemplateIdMap = {
  /** 店铺设置 */
  ShopSetting: -1,
  /** 适用店铺 */
  ApplicableStores: -2,
  /** 商品售罄 */
  GoodsSoldout: 41,
  /** 商品售罄提醒 */
  GoodsSoldoutReminder: 42,
  /** 商品淘汰 */
  GoodsDiscard: 43,
  /** 商品下架 */
  GoodsOffShelf: 44,
  /** 商品解绑 */
  GoodsUnbind: 45,
  /** 订单就餐 */
  OrderDiningout: 46,
  /** 订单售后 */
  OrderAftersale: 47,
  /** 订单超时 */
  OrderTimeout: 48,
  /** 订单取餐状态 */
  OrderPickupStatus: 49,
};

export const OperatorType = {
  /** 小于 */
  LessThan: 1,
  /** 小于等于 */
  LessThanOrEqual: 2,
  /** 等于 */
  Equals: 3,
  /** 大于 */
  GreaterThan: 4,
  /** 大于等于 */
  GreaterThanOrEqual: 5,
  /** 包含 */
  In: 7,
};

export const OperatorText = {
  [OperatorType.Equals]: '=',
  [OperatorType.GreaterThan]: '>',
  [OperatorType.GreaterThanOrEqual]: '>=',
  [OperatorType.LessThan]: '<',
  [OperatorType.LessThanOrEqual]: '<=',
  [OperatorType.In]: '包含',
};
