.card {
  background: #fff;
  border-radius: 4px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  height: 66px;

  .card-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;

    .card-image {
      // object-fit: cover;
      border-radius: 4px;
      width: 48px;
      height: 48px;
    }

    .card-text {
      .title {
        font-size: 16px;
      }
      .help-desc {
        margin-top: 8px;
        line-height: 18px;
        color: #999999;
        font-size: 12px;
        width: 220px;
      }
    }
  }

  // .action-buttons {
  //   display: flex;
  //   gap: 8px;
  //   flex-shrink: 0;
  //   flex-wrap: wrap;
  // }
}
