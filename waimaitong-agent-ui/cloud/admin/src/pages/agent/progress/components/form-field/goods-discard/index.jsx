import React, { forwardRef, useEffect } from 'react';
import { FormInputField } from 'zent';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import MsgChannelField from '../base-field/msg-channel-field';
import ReceiversField from '../base-field/receivers-field';
import BaseForm from '../base-form';

const GoodsDiscardForm = forwardRef(
  ({ yz, skill, agentId, onConfirm, value }, ref) => {
    useEffect(() => {
      const { form } = ref.current;
      if (form) {
        form.initialize(value);
      }
    }, []);

    return (
      <BaseForm ref={ref} onSubmit={onConfirm}>
        <FieldGroup title="执行条件">
          <ConditionField
            name="conditions"
            conditionOptions={[
              { key: 30, text: '近30天销量' },
              { key: 60, text: '近60天销量' },
              { key: 90, text: '近90天销量' },
            ]}
            operatorOptions={[{ key: 'lessThanOrEqual', text: '<=' }]}
            disabled={{
              operator: true,
              remove: true,
            }}
            maxLength={1}
          />
        </FieldGroup>

        <FieldGroup title="执行操作">
          <MsgChannelField />

          <ReceiversField yz={yz} />

          <FormInputField
            name="notifyContent"
            label="通知文案："
            props={{
              placeholder: '请输入通知文案',
              rows: 4,
              type: 'textarea',
              showCount: true,
              width: 400,
              maxLength: 200,
              disabled: true,
            }}
            defaultValue="截止[XX]年[XX]月[XX]日，[XX]门店的[XX商品名称]命中[XX外卖渠道]近XX天总销量为X的规则，请及时介入处理。"
          />
        </FieldGroup>
      </BaseForm>
    );
  }
);

export default GoodsDiscardForm;
