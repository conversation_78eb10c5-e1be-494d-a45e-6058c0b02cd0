export const SyncFieldMap = [
  {
    group: 'stockInfo',
    groupLabel: '库存数量',
    fields: [
      {
        name: 'stockNum',
        label: '商品库存',
      },
    ],
  },
  {
    group: 'baseInfo',
    groupLabel: '基础信息',
    fields: [
      {
        name: 'title',
        label: '商品/规格名称',
      },
      {
        name: 'category',
        label: '商品类目',
      },
      {
        name: 'prop',
        label: '商品属性',
      },
      {
        name: 'picture',
        label: '商品主图',
      },
      {
        name: 'group',
        label: '商品分组',
      },
      {
        name: 'sellPoint',
        label: '商品描述',
      },
      {
        name: 'groupSequence',
        label: '分组排序',
        hidden: true,
      },
    ],
  },
  {
    group: 'priceInfo',
    groupLabel: '价格信息',
    fields: [
      {
        name: 'price',
        label: '商品价格',
      },
      {
        name: 'packingCharge',
        label: '打包费',
      },
    ],
  },
  {
    group: 'otherInfo',
    groupLabel: '其他信息',
    fields: [
      {
        name: 'minimumPurchaseNum',
        label: '最小起购量',
      },
    ],
  },
];

export const AllFieldsMap = SyncFieldMap.reduce((result, group) => {
  result[group.group] = group.fields.map((field) => field.name);
  return result;
}, {});
