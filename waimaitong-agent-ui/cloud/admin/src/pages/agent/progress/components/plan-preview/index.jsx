/* eslint-disable react/prop-types */
import React from 'react';
import { BlockLoading, Button, Notify, Icon } from 'zent';
import { AgentMessageBlockTypeEnum, SkillTemplateIdMap } from '../../constants';
import { getFormattedDate, isEmpty } from '../../utils';
import { AllFieldsMap } from '../form-field/shop-setting/constants';
import AgentBlockWrapper from '../ui/agent-block-wrapper';
import Container from '../ui/container';
import DocumentLayout from '../ui/document-layout';
import Footer from '../ui/footer';
import { DefaultValues } from './default-values';
import { SyncFieldMap } from '../form-field/shop-setting/constants';
import {
  getGoodsSettingDesc,
  getSectionsDesc,
  filterSections,
} from '../../utils';
import { TestShopIds } from '../../../constant';
import './index.scss';

const learningConfigs = {
  type: AgentMessageBlockTypeEnum.THOUGHT_CHAIN,
  isStreamTyping: true,
  thoughtChain: {
    isAutoCollapse: true,
    title: '正在深度思考...',
    currentStep: 0,
    steps: [
      {
        topic: '正在分析小店运行目标',
        content: `我现在要为你量身定制外卖渠道店铺托管方案，我需要先理解您的需求核心，将店铺外卖业务交给系统智能运营，或者自己管理时设置一些策略，提高外卖运营管理效率。有以下关键点，我需要逐一学习并思考每个点的重要性以及如何影响最终托管方案设计。  
第一，确定运营目标，主要目的是提升全外卖渠道业务的运营效率。  
第二，托管店铺的高频操作，依据店铺行业属性，适配渠道规则模板，将应用到未来增量新开的外卖店铺，存量外卖店铺已有设置不受影响。  
第三，动态识别商品售罄、商品销量、异常解绑场景，依据你期望的策略自动化执行对应任务。比如：库存售罄时通知相关店员或者自动下架。  
第四，凭借高效数据抓取和分析能力，实时获取来自美团、饿了么、京东外卖等多平台订单信息。当新订单生成，智能体快速响应，读取订单详情，包括出餐状态、接单状态、取餐状态等关键数据。比如智能出餐，帮你规划出餐先后顺序和流程，实时调取三方外卖接口实现上报出餐，并记录出餐用时、出餐完成时间、订单号，以便后续统计和分析。  
综上，推荐适配的外卖托管方案如下：`,
      },
      // {
      //   topic:"已生成外卖托管方案",
      //   content:''
      // }
    ],
  },
};
const previewConfigs = {
  type: AgentMessageBlockTypeEnum.TEXT,
  markdown: '我为你生成了可以自由编辑的托管方案，请确认方案：',
  isStreamTyping: true,
};

const timeoutConfig = {
  previewBlock: 15000,
  document: 17000,
  footer: 19000,
};

class PlanPreview extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      shopName: '',
      showPreviewBlock: false,
      showDocument: false,
      showFooter: false,
      formValue: DefaultValues,
      loading: false,
    };

    this.timers = [];
    this.itemSelectorRef = null;
  }

  get isTestShop() {
    const { yz: yzProps } = this.props;
    const { kdtId } = yzProps.data.shop;
    return TestShopIds.includes(kdtId);
  }

  componentDidMount() {
    this.timers = [
      setTimeout(() => {
        this.setState({ showPreviewBlock: true }, () => {
          learningConfigs.thoughtChain.title = '已深度思考';
          learningConfigs.thoughtChain.currentStep = 1;
          learningConfigs.thoughtChain.steps[0].topic = '已分析小店运营目标';
        });
      }, timeoutConfig.previewBlock),
      setTimeout(() => {
        this.setState({ showDocument: true });
      }, timeoutConfig.document),
      setTimeout(() => {
        this.setState({ showFooter: true });
      }, timeoutConfig.footer),
    ];

    const { shop = {} } = this.yz.data || {};
    this.setState({ shopName: shop.shopName });

    this.setState({ loading: true });

    Promise.all([
      this.fetchShopSetting(),
      this.yz
        .queryComponentById('waimaitong-item-selector')
        .then((itemSelector) => {
          this.itemSelectorRef = itemSelector;
        }),
    ]).finally(() => {
      this.setState({ loading: false });
    });
  }

  componentWillUnmount() {
    this.timers.forEach(clearTimeout);
  }

  fetchShopSetting = () => {
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/queryWmShopHostConfig',
      useBizRequest: true,
      method: 'get',
    })
      .then((res) => {
        const { channelAiConfigDetailDTO } = res.data.data;
        const {
          operateStatus,
          syncBusinessHours,
          allowOpenInvoice,
          stockThreshold,
          shopRole,
          outItemInfoSync: outItemInfoSyncStr,
        } = channelAiConfigDetailDTO;

        let outItemInfoSync = {};
        try {
          outItemInfoSync = JSON.parse(outItemInfoSyncStr);
        } catch (err) {
          console.log('err', err);
        }

        const formData = {
          operateStatus,
          syncBusinessHours,
          allowOpenInvoice,
          stockThreshold: stockThreshold <= 15 ? 15 : 50,
        };

        // 从接口返回的数据中提取字段值
        const getSyncInfo = (outItemInfoSync, fields = []) => {
          return fields.filter((field) => outItemInfoSync[field]);
        };

        // 使用 AllFieldsMap 提取已选中的字段
        Object.keys(AllFieldsMap).forEach((groupName) => {
          formData[groupName] = getSyncInfo(
            outItemInfoSync,
            AllFieldsMap[groupName]
          );
        });

        this.setState({
          formValue: {
            ...this.state.formValue,
            [SkillTemplateIdMap.ShopSetting]: {
              ...this.state.formValue[SkillTemplateIdMap.ShopSetting],
              ...formData,
              shopRole: Boolean(shopRole),
            },
          },
        });
      })
      .catch((err) => {
        console.error('fetchShopSetting', err);
      });
  };

  handleEdit = (skillTemplateId, values) => {
    this.setState({
      formValue: {
        ...this.state.formValue,
        [skillTemplateId]: {
          ...this.state.formValue[skillTemplateId],
          ...values,
        },
      },
    });
  };

  getSections() {
    // console.log("debugger form", this.state.formValue);

    const {
      [SkillTemplateIdMap.ShopSetting]: shopValue = {},
      [SkillTemplateIdMap.GoodsDiscard]: discardValue = {},
      [SkillTemplateIdMap.OrderDiningout]: diningoutValue = {},
    } = this.state.formValue || {};

    const sections = [
      {
        id: 'section-1',
        title: '一、运营目标',
        content: `提升全外卖渠道业务运营效率，减少人工投入。`,
      },
      {
        id: 'section-2',
        key: '',
        title: '二、店铺设置托管',
        editable: true,
        skillTemplateId: SkillTemplateIdMap.ShopSetting,
        bulletPoints: [
          {
            label: '店铺经营状态',
            desc: shopValue.operateStatus ? '营业' : '休息',
          },
          {
            label: '营业时间同步',
            desc: shopValue.syncBusinessHours ? '同步' : '不同步',
          },
          {
            label: '库存同步时效',
            desc: shopValue.stockThreshold === 50 ? '超高要求' : '中等要求',
          },
          {
            label: '有赞可修改外卖商品哪些信息',
            desc: getGoodsSettingDesc(SyncFieldMap, shopValue),
          },
          {
            label: '扫小票二维码开票',
            desc: shopValue.allowOpenInvoice ? '开启' : '关闭',
          },
        ],
      },
      {
        id: 'section-3',
        key: '',
        title: '三、商品托管',
        addable: true,
        bulletPoints: [
          {
            label: '库存为 0 时',
            desc: <span>给店铺管理员发送提醒</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.GoodsSoldout,
          },
          {
            label: '商品异常解绑',
            desc: <span>给店铺管理员发送提醒</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.GoodsUnbind,
          },
          {
            label: '月度商品汰换',
            desc: `每月1号自动下架各外卖渠道近${
              discardValue?.conditions?.[0]?.field || 30
            }天动销≤0的商品`,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.GoodsDiscard,
          },
        ],
      },
      {
        id: 'section-4',
        key: '',
        title: '四、订单托管',
        addable: true,
        bulletPoints: [
          {
            label: '自动出餐',
            desc: (
              <span>
                {isEmpty(diningoutValue.instantPeriods) ||
                isEmpty(diningoutValue.reservationPeriods)
                  ? '关闭'
                  : '开启'}
              </span>
            ),
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.OrderDiningout,
            hidden: this.isTestShop,
          },
          {
            label: '未发货自动同意退款',
            desc: '开启',
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.OrderAftersale,
          },
          {
            label: '骑手长时间未接单时',
            desc: <span>通知店铺管理员</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.OrderTimeout,
          },
          {
            label: '骑手长时间未取餐时',
            desc: <span>通知店铺管理员</span>,
            editable: true,
            key: '',
            skillTemplateId: SkillTemplateIdMap.OrderPickupStatus,
          },
        ],
      },
      {
        id: 'section-5',
        key: '',
        title: '五、适用范围',
        editable: false,
        skillTemplateId: SkillTemplateIdMap.ApplicableStores,
        bulletPoints: [
          { label: '适用店铺', desc: '全部外卖店铺' },
          {
            label: '托管方案适用新增外卖店铺',
            desc: '是',
          },
          {
            label: '适用渠道',
            desc: (
              <span>
                美团外卖、饿了么外卖、京东外卖、美团闪购
                <span className="plan-preview__desc">
                  (若当前有渠道未开通，则会在渠道开通后立即生效)
                </span>
              </span>
            ),
          },
        ],
      },
    ];

    // 过滤掉带有 hidden 属性的 section 和 bulletPoint
    return sections
      .filter((section) => !section.hidden)
      .map((section) => ({
        ...section,
        bulletPoints:
          section.bulletPoints?.filter((point) => !point.hidden) || [],
      }));
  }

  handleNextClick = () => {
    const { formValue } = this.state;
    const formValueValidResult = Object.keys(formValue).map(
      (skillTemplateIdStr) => {
        const skillTemplateId = +skillTemplateIdStr;

        const formItem = formValue[skillTemplateId];

        if (formItem.enable) {
          if (
            skillTemplateId === SkillTemplateIdMap.GoodsSoldout ||
            skillTemplateId === SkillTemplateIdMap.GoodsDiscard ||
            skillTemplateId === SkillTemplateIdMap.GoodsUnbind ||
            skillTemplateId === SkillTemplateIdMap.OrderTimeout ||
            skillTemplateId === SkillTemplateIdMap.OrderPickupStatus
          ) {
            return (
              formItem.msgChannels.length > 0 && formItem.receivers.length > 0
            );
          }
          if (
            skillTemplateId === SkillTemplateIdMap.OrderDiningout &&
            !this.isTestShop
          ) {
            return (
              formItem.instantPeriods.length > 0 ||
              formItem.reservationPeriods.length > 0
            );
          }
          return true;
        }
        return true;
      }
    );

    if (formValueValidResult.some((item) => !item)) {
      Notify.error('存在未填写信息，请完善技能内容');
      return;
    }

    const sections = filterSections(this.getSections(), this.state.formValue);
    const planList = getSectionsDesc(sections);

    this.props.handleNextStep();
    this.props.onFormValueChange(formValue);
    this.props.onPlanListChange(planList);
  };

  render() {
    const {
      showPreviewBlock,
      showDocument,
      showFooter,
      formValue,
      loading,
      shopName,
    } = this.state;

    const title = `${shopName ? `${shopName} | ` : ''}外卖托管方案`;
    const subtitle = `运营专员：全网外卖通托管  |  汇报时间：${getFormattedDate()}`;

    return (
      <div className="plan-preview">
        <BlockLoading loading={loading}>
          <Container>
            <AgentBlockWrapper>
              <AgentMessageBlock block={learningConfigs} />
              {showPreviewBlock && (
                <div className="plan-preview__block">
                  <AgentMessageBlock block={previewConfigs} />
                </div>
              )}
              {showDocument && (
                <DocumentLayout
                  title={title}
                  subtitle={subtitle}
                  sections={this.getSections()}
                  onEdit={this.handleEdit}
                  yz={yz}
                  formValue={formValue}
                  itemSelectorRef={this.itemSelectorRef}
                />
              )}
              {/* <DocumentLayout
                title={title}
                subtitle={subtitle}
                sections={this.getSections()}
                onEdit={this.handleEdit}
                yz={yz}
                formValue={formValue}
                itemSelectorRef={this.itemSelectorRef}
              /> */}
            </AgentBlockWrapper>
            {showFooter && (
              <Footer>
                <Button onClick={this.handleNextClick} type="primary">
                  下一步
                </Button>
              </Footer>
            )}
          </Container>
          <ItemSelectorDialog
            cloudId="waimaitong-item-selector"
            shopType={7}
            itemSelectorConfigList={[
              {
                type: 'item',
                configList: [
                  {
                    channelId: [200, 201, 203],
                    isShowItemTypeSelect: false,
                    isItemGroupSelectMultiple: true,
                  },
                ],
              },
            ]}
          />
        </BlockLoading>
      </div>
    );
  }
}

export default PlanPreview;
