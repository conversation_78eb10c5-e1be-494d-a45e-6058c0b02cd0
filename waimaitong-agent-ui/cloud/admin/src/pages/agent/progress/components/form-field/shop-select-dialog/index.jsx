import React from "react";
import { Dialog, Button, Icon } from "zent";
import Content from "./content";
import "./index.scss";

class ShopSelectDialog extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      shopList: [],
      visible: false,
      applicableType: 1,
    };
  }

  setVisible = (value) => {
    this.setState({ visible: value });
  };

  handleConfirm = () => {
    const { applicableType, shopList } = this.state;
    this.props.onConfirm({
      storeIds: shopList.map((shop) => shop.key),
      applicableType,
    });
    this.setVisible(false);
  };

  handleApplicableType = (e) => {
    this.setState({ applicableType: e.target.value });
  };

  handleShopList = (value) => {
    this.setState({ shopList: value });
  };

  render() {
    return (
      <div className="shop-select">
        <span onClick={() => this.setVisible(true)} className="editable-icon">
          <Icon type="edit-o" className="editable-icon-icon" />
          修改
        </span>
        <Dialog
          className="shop-select__dialog"
          visible={this.state.visible}
          onClose={() => this.setVisible(false)}
          footer={
            <>
              <Button onClick={() => this.setVisible(false)}>取消</Button>
              <Button type="primary" onClick={this.handleConfirm}>
                确定
              </Button>
            </>
          }
          title="修改适用范围"
        >
          <Content
            applicableType={this.state.applicableType}
            shopList={this.state.shopList}
            handleApplicableType={this.handleApplicableType}
            handleShopList={this.handleShopList}
          />
        </Dialog>
      </div>
    );
  }
}

export default ShopSelectDialog;
