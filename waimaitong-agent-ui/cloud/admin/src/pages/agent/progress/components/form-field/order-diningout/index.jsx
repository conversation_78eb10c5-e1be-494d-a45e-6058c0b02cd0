import React, { forwardRef, useEffect } from 'react';
import { FormDescription, Icon } from 'zent';
import FieldGroup from '../../field-group';
import GoodsSelectorField from '../base-field/goods-selector-field';
import TimePeriodField from '../base-field/time-period-field';
import BaseForm from '../base-form';
import './index.scss';

const OrderDiningoutForm = forwardRef(
  ({ yz, skill, itemSelectorRef, agentId, onConfirm, value }, ref) => {
    useEffect(() => {
      const { form } = ref.current;

      if (form) {
        form.initialize(value);
      }
    }, []);

    return (
      <BaseForm ref={ref} onSubmit={onConfirm}>
        <FormDescription className="order-diningout-form-description">
          <Icon type="info-circle-o" />
          支持智能出餐，省去人工手动操作，如需临时暂停请及时关闭技能。
          <a
            onClick={() => {
              yz.navigateTo({
                route:
                  'https://bbs.youzan.com/forum.php?mod=viewthread&tid=699480',
                blank: true,
              });
            }}
          >
            查看操作示例
          </a>
        </FormDescription>

        <FieldGroup title="即时单时段设置">
          <TimePeriodField
            name="instantPeriods"
            periodLabel="时段"
            afterTimeLabel="接单后"
            maxLength={5}
          />
        </FieldGroup>

        <FieldGroup title="预定单时段设置">
          <TimePeriodField
            name="reservationPeriods"
            periodLabel="时段"
            afterTimeLabel="预约送达时间前"
            maxLength={5}
          />
        </FieldGroup>
        <FieldGroup
          title="适用商品设置"
          description="只有订单中的商品全部是自动出餐商品时，此订单才会自动出餐"
        >
          <GoodsSelectorField itemSelectorRef={itemSelectorRef} />
          <FormDescription className="order-diningout-bottom-description">
            <Icon type="warning" />
            智能出餐无法完全规避潜在风险，任何风险及相应责任商户开启功能后均由商户自行承担，线下请按时间要求出餐。
          </FormDescription>
        </FieldGroup>
      </BaseForm>
    );
  }
);

export default OrderDiningoutForm;
