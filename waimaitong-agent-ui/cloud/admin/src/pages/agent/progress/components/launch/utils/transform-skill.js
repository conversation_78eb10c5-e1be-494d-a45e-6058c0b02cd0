import { AllFieldsMap } from '../../../components/form-field/shop-setting/constants';
import { SkillTemplateIdMap } from '../../../constants';

export const transformShopSkill = ({ formValue }) => {
  const shopSettingSkill = formValue[SkillTemplateIdMap.ShopSetting];

  const { allowOpenInvoice, stockThreshold, syncBusinessHours, operateStatus } =
    shopSettingSkill;

  const outItemInfoSync = {};

  // 使用 AllFieldsMap 处理表单数据
  Object.keys(AllFieldsMap).forEach((groupName) => {
    const fieldNames = AllFieldsMap[groupName];
    const selectedValues = shopSettingSkill[groupName] || [];

    fieldNames.forEach((fieldName) => {
      outItemInfoSync[fieldName] = selectedValues.includes(fieldName) ? 1 : 0;
    });
  });

  const params = {
    allowOpenInvoice,
    operateStatus,
    stockThreshold,
    syncBusinessHours,
    outItemInfoSync: JSON.stringify(outItemInfoSync),
  };

  return {
    enable: shopSettingSkill.enable,
    channelAiConfigDetailDTO: params,
  };
};

export const transformGoodsSkill = ({ formValue, agentId, skillList }) => {
  const goodsSoldoutSkill = formValue[SkillTemplateIdMap.GoodsSoldout];

  const goodsUnbindSkill = formValue[SkillTemplateIdMap.GoodsUnbind];

  const goodsDiscardSkill = formValue[SkillTemplateIdMap.GoodsDiscard];

  const goodsSoldoutParams = {
    id: skillList.find(
      (item) => item.skillTemplateId === SkillTemplateIdMap.GoodsSoldout
    )?.id,
    templateId: SkillTemplateIdMap.GoodsSoldout,
    enable: goodsSoldoutSkill.enable,
    action: {
      msgChannels: goodsSoldoutSkill.msgChannels,
      receivers: goodsSoldoutSkill.receivers,
    },
  };

  const goodsSoldoutReminderParams = {
    id: skillList.find(
      (item) => item.skillTemplateId === SkillTemplateIdMap.GoodsSoldoutReminder
    )?.id,
    templateId: SkillTemplateIdMap.GoodsSoldoutReminder,
    enable: goodsSoldoutSkill.enable,
  };

  const goodsUnbindParams = {
    id: skillList.find(
      (item) => item.skillTemplateId === SkillTemplateIdMap.GoodsUnbind
    )?.id,
    templateId: SkillTemplateIdMap.GoodsUnbind,
    enable: goodsUnbindSkill.enable,
    action: {
      msgChannels: goodsUnbindSkill.msgChannels,
      receivers: goodsUnbindSkill.receivers,
    },
  };

  const goodsDiscardParams = {
    id: skillList.find(
      (item) => item.skillTemplateId === SkillTemplateIdMap.GoodsDiscard
    )?.id,
    templateId: SkillTemplateIdMap.GoodsDiscard,
    enable: goodsDiscardSkill.enable,
    action: {
      msgChannels: goodsDiscardSkill.msgChannels,
      receivers: goodsDiscardSkill.receivers,
    },
    condition: {
      salesVolumeCondition: {
        period: +goodsDiscardSkill.conditions[0].field,
        salesVolume: +goodsDiscardSkill.conditions[0].value,
      },
    },
  };

  const goodsOffShelfParams = {
    id: skillList.find(
      (item) => item.skillTemplateId === SkillTemplateIdMap.GoodsOffShelf
    )?.id,
    templateId: SkillTemplateIdMap.GoodsOffShelf,
    enable: goodsDiscardSkill.enable,
  };

  return {
    agentId,
    skills: [
      goodsSoldoutParams,
      goodsSoldoutReminderParams,
      goodsUnbindParams,
      goodsDiscardParams,
      goodsOffShelfParams,
    ],
  };
};

const buildConditionConfig = (values) => {
  const { conditions } = values;
  const result = {};
  const deliveryStatusCondition = conditions.find(
    (item) => item.field === 'deliveryStatus'
  );

  const orderSourceCondition = conditions.find(
    (item) => item.field === 'orderSource'
  );

  const refundAmountCondition = conditions.find(
    (item) => item.field === 'refundAmount'
  );

  result.shippingConfig = {
    operatorType: deliveryStatusCondition.operator,
    orderStatus: 40,
  };

  result.sourceConfig = {
    operatorType: orderSourceCondition.operator,
    salesChannelId: [20, 120, 130, 140],
  };

  if (refundAmountCondition) {
    result.amountMaxConfig = {
      operatorType: refundAmountCondition.operator,
      amountValue: refundAmountCondition.value * 1000,
    };
  }

  return result;
};

export const transformOrderSkill = ({ formValue, agentId, skillList }) => {
  const orderDiningoutSkill = formValue[SkillTemplateIdMap.OrderDiningout];
  const orderDiningoutSkillItem = skillList.find(
    (item) => item.skillTemplateId === SkillTemplateIdMap.OrderDiningout
  );

  const orderAftersaleSkill = formValue[SkillTemplateIdMap.OrderAftersale];
  const orderAftersaleSkillItem = skillList.find(
    (item) => item.skillTemplateId === SkillTemplateIdMap.OrderAftersale
  );

  const orderTimeoutSkill = formValue[SkillTemplateIdMap.OrderTimeout];
  const orderTimeoutSkillItem = skillList.find(
    (item) => item.skillTemplateId === SkillTemplateIdMap.OrderTimeout
  );

  const orderPickupStatusSkill =
    formValue[SkillTemplateIdMap.OrderPickupStatus];
  const orderPickupStatusSkillItem = skillList.find(
    (item) => item.skillTemplateId === SkillTemplateIdMap.OrderPickupStatus
  );

  const orderDiningoutParams = {
    enable: orderDiningoutSkill.enable,
    skillId: orderDiningoutSkillItem?.id,
    skillName: orderDiningoutSkillItem?.name,
    skillType: 1,
    skillAction: {
      autoConfirmAction: {
        actionName: '自动出餐',
        actionType: 1,
      },
    },
    skillCondition: {
      instantOrderConfig: orderDiningoutSkill.instantPeriods.map((item) => ({
        startTime: item.timeRange[0],
        endTime: item.timeRange[1],
        offsetTime: item.delay,
        offsetTimeType: 2,
        timeUnit: 1,
      })),
      appointmentOrderConfig: orderDiningoutSkill.reservationPeriods.map(
        (item) => ({
          startTime: item.timeRange[0],
          endTime: item.timeRange[1],
          offsetTime: item.delay,
          offsetTimeType: 2,
          timeUnit: 1,
        })
      ),
      applicableGoodsConfig: {
        applicableType: orderDiningoutSkill.applicableGoods.applicableType,
        applicableGoodsIdList: orderDiningoutSkill.applicableGoods.itemIds,
      },
    },
  };

  const orderAftersaleParams = {
    enable: orderAftersaleSkill.enable,
    skillId: orderAftersaleSkillItem?.id,
    skillName: orderAftersaleSkillItem?.name,
    skillType: 2,
    skillAction: {
      autoRefundAction: {
        actionName: '自动退款',
        actionType: 2,
      },
    },
    skillCondition: buildConditionConfig(orderAftersaleSkill),
  };

  const orderTimeoutParams = {
    enable: orderTimeoutSkill.enable,
    skillId: orderTimeoutSkillItem?.id,
    skillName: orderTimeoutSkillItem?.name,
    skillType: 3,
    skillCondition: {
      deliveryOrderingConfig: {
        waitOrderingTime: orderTimeoutSkill.conditions[0].value,
        waitTimeOperatorType: 35,
        timeUnit: 1,
      },
    },
    skillAction: {
      pushAction: {
        actionType: 4,
        actionName: '消息推送',
        pushChannelList: orderTimeoutSkill.msgChannels,
        staffIdList: orderTimeoutSkill.receivers,
      },
    },
  };

  const orderPickupStatusParams = {
    enable: orderPickupStatusSkill.enable,
    skillId: orderPickupStatusSkillItem?.id,
    skillName: orderPickupStatusSkillItem?.name,
    skillType: 4,
    skillCondition: {
      deliveryTakingConfig: {
        waitTakingTime: orderPickupStatusSkill.conditions[0].value,
        waitTimeOperatorType: 35,
        timeUnit: 1,
      },
    },
    skillAction: {
      pushAction: {
        actionType: 4,
        actionName: '消息推送',
        pushChannelList: orderPickupStatusSkill.msgChannels,
        staffIdList: orderPickupStatusSkill.receivers,
      },
    },
  };

  return {
    agentId,
    skillList: [
      orderDiningoutParams,
      orderAftersaleParams,
      orderTimeoutParams,
      orderPickupStatusParams,
    ],
  };
};
