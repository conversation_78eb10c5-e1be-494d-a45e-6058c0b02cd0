import React, { forwardRef, useEffect } from 'react';
import { FormControl } from 'zent';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import BaseForm from '../base-form';

const GoodsSoldoutReminderForm = forwardRef(({ value }, ref) => {
  const handleSubmit = () => {
    return Promise.resolve();
  };

  useEffect(() => {
    const { form } = ref.current;
    if (form) {
      form.initialize(value);
    }
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={handleSubmit}>
      <FieldGroup title="执行条件">
        <ConditionField
          name="conditions"
          disabled
          operatorOptions={[{ key: 'equals', text: '=' }]}
          conditionOptions={[{ key: 'stock', text: '库存数量' }]}
        />
      </FieldGroup>

      <FieldGroup title="执行操作">
        <FormControl withoutLabel>在对应店铺外卖渠道自动下架</FormControl>
      </FieldGroup>
    </BaseForm>
  );
});

export default GoodsSoldoutReminderForm;
