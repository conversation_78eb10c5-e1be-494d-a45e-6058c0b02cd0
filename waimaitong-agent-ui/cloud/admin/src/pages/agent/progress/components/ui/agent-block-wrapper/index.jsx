import React from "react";
import "./index.scss";

class AgentBlockWrapper extends React.Component {

  static defaultProps = {
    showIcon: true,
    iconUrl: 'https://img01.yzcdn.cn/upload_files/2025/03/20/FndqMkz1Dea-AbAF_jHXvrE_Hqlb.png'
  };

  render() {
    return (
      <div className="wrapper">
        {
          this.props.showIcon && (
        <img
          className="wrapper__icon"
          alt="icon"
          src={
            this.props.iconUrl
          }
        />
          )
        }
        
        {this.props.children}
      </div>
    );
  }
}

export default AgentBlockWrapper;
