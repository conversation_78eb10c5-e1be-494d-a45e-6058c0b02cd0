.condition-container {
  background-color: #F7F7F7;
  padding: 12px 0;
  border-radius: 4px;
}

.condition-item {
  display: flex;
  position: relative;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.condition-content {
  display: flex;
}

.condition-joint {
  width: 30px;
  
  &-line {
    width: 11px;
    height: calc(100% - 57px);
    border-left: 1px solid #999;
    border-top: 1px solid #999;
    border-bottom: 1px solid #999;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    transform: translateX(15px) translateY(17px);
    position: relative;
    &::before {
      content: '且';
      position: absolute;
      top: 50%;
      left: 0;
      font-size: 14px;
      color: #999;
      line-height: 22px;
      transform: translateX(-7px) translateY(-50%);
      background-color: #F7F7F7;
    }
  }
}

.condition-items-wrapper {
  flex: 1;
}

.condition-content {
  display: flex;
}

.condition-remove {
  cursor: pointer;
  color: #999;
  font-size: 15px;
  color: #4A4A4A;
  transform: translateY(7px);
  margin-left: 8px;
}

.add-condition-btn {
  margin-left: 40px;
  cursor: pointer;
  color: #333;
}