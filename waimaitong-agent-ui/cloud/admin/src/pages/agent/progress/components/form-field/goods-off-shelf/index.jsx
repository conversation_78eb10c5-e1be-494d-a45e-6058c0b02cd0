import React, { forwardRef, useEffect } from 'react';
import { FormControl, FormDescription } from 'zent';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import BaseForm from '../base-form';

const GoodsOffShelfForm = forwardRef(({ value }, ref) => {
  const handleSubmit = (values) => {
    console.log('提交商品下架表单数据', values);
    return Promise.resolve();
  };

  useEffect(() => {
    const { form } = ref.current;
    if (form) {
      form.initialize(value);
    }
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={handleSubmit}>
      <FieldGroup title="执行条件">
        <ConditionField
          name="conditions"
          disabled
          conditionOptions={[{ key: 'sellCount', text: '近30天销量' }]}
          operatorOptions={[{ key: 'equals', text: '=' }]}
        />
      </FieldGroup>

      <FieldGroup title="执行操作">
        <FormControl withoutLabel>
          自动下架待淘汰商品
          <FormDescription>
            下架记录可前往智能体历史执行记录查看
          </FormDescription>
        </FormControl>
      </FieldGroup>
    </BaseForm>
  );
});

export default GoodsOffShelfForm;
