import React from "react";
import { Radio } from "zent";
const RadioGroup = Radio.Group;
import "./index.scss";

class Content extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      shopList: [],
    };
  }

  async componentDidMount() {
    try {
      const cloudComponent = await this.yz.queryComponentById("shop-selector");

      const shopList = this.props.shopList;
      if (shopList.length > 0) {
        cloudComponent.setValue(shopList);
      }

      cloudComponent.onChange((value) => {
        this.setState({ shopList: value });
        this.props.handleShopList(value);
      });
    } catch (error) {
      console.error("Error in ShopSelectDialog:", error);
    }
  }

  render() {
    return (
      <div className="content">
        <span>适用范围：</span>
        <RadioGroup
          onChange={this.props.handleApplicableType}
          value={this.props.applicableType}
        >
          <Radio value={1}>全部店铺</Radio>
          <Radio value={2}>指定店铺</Radio>
          {this.props.applicableType === 2 && (
            <UnionShopSelect
              cloudId="shop-selector"
              cloudClass="shop-selector"
              isMultiple={true}
              width={240}
              popupWidth={240}
            />
          )}
        </RadioGroup>
      </div>
    );
  }
}

export default Content;
