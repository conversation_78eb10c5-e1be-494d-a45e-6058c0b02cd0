import React, { forwardRef, useEffect } from 'react';
import { FormDescription } from 'zent';
import { OperatorText, OperatorType } from '../../../constants';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import BaseForm from '../base-form';

const conditionOptions = [
  { key: 'deliveryStatus', text: '发货状态', disabled: true },
  { key: 'orderSource', text: '订单来源', disabled: true },
  { key: 'refundAmount', text: '退款金额' },
];

const operatorOptions = Object.entries(OperatorType).map(([, text]) => {
  return {
    key: text,
    text: OperatorText[text],
    disabled: text === OperatorType.In,
  };
});

const OrderAftersaleForm = forwardRef(
  ({ yz, skill, agentId, onConfirm, value }, ref) => {
    useEffect(() => {
      const { form } = ref.current;
      if (form) {
        form.initialize(value);
      }
    }, []);

    return (
      <BaseForm ref={ref} onSubmit={onConfirm}>
        <FieldGroup title="执行条件">
          <ConditionField
            name="conditions"
            conditionOptions={conditionOptions}
            operatorOptions={operatorOptions}
            maxLength={3}
            defineAddCondition={() => ({
              props: {
                valueProps: {
                  min: 0,
                  max: 10e6 - 1,
                  addonAfter: '元',
                  numerical: true,
                },
              },
            })}
          />
        </FieldGroup>

        <FieldGroup title="执行操作">
          <FormDescription>自动同意消费者取消订单</FormDescription>
        </FieldGroup>
      </BaseForm>
    );
  }
);

export default OrderAftersaleForm;
