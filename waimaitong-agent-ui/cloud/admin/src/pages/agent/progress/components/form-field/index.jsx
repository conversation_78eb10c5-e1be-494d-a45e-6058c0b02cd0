import React from 'react';
import EditDrawer from '../edit-drawer';
import GoodsDiscardForm from './goods-discard';
import GoodsOffShelfForm from './goods-off-shelf';
import GoodsSoldoutForm from './goods-soldout';
import GoodsSoldoutReminderForm from './goods-soldout-reminder';
import GoodsUnbindForm from './goods-unbind';
import OrderAftersaleForm from './order-aftersale';
import OrderDiningoutForm from './order-diningout';
import OrderPickupStatusForm from './order-pickup-status';
import OrderTimeoutForm from './order-timeout';
import ShopSettingForm from './shop-setting';

// 使用示例
export const ShopSettingDrawer = ({ yz, agentId, onConfirm, value }) => (
  <EditDrawer title="店铺托管设置">
    <ShopSettingForm
      yz={yz}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const GoodsSoldoutDrawer = ({
  yz,
  skill,
  agentId,
  onConfirm,
  value,
  showWarning
}) => (
  <EditDrawer title="库存售罄提醒" showWarning={showWarning}>
    <GoodsSoldoutForm
      yz={yz}
      skill={skill}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const GoodsSoldoutReminderDrawer = ({
  yz,
  skill,
  agentId,
  onConfirm,
  value,
  showWarning
}) => (
  <EditDrawer title="库存售罄提醒" showWarning={showWarning}>
    <GoodsSoldoutReminderForm
      yz={yz}
      skill={skill}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const GoodsDiscardDrawer = ({
  yz,
  skill,
  agentId,
  onConfirm,
  value,
  showWarning
}) => (
  <EditDrawer title="商品淘汰提醒" showWarning={showWarning}>
    <GoodsDiscardForm
      yz={yz}
      skill={skill}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const GoodsOffShelfDrawer = ({
  yz,
  skill,
  agentId,
  onConfirm,
  value,
  showWarning
}) => (
  <EditDrawer title="商品下架提醒" showWarning={showWarning}>
    <GoodsOffShelfForm
      yz={yz}
      skill={skill}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const GoodsUnbindDrawer = ({ yz, skill, agentId, onConfirm, value,showWarning }) => (
  <EditDrawer title="商品异常解绑监控" showWarning={showWarning}>
    <GoodsUnbindForm
      yz={yz}
      skill={skill}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const OrderDiningoutDrawer = ({
  yz,
  skill,
  itemSelectorRef,
  agentId,
  onConfirm,
  value,
  showWarning
}) => (
  <EditDrawer title="自动出餐" width={750} showWarning={showWarning}>
    <OrderDiningoutForm
      yz={yz}
      skill={skill}
      itemSelectorRef={itemSelectorRef}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const OrderAftersaleDrawer = ({
  yz,
  skill,
  agentId,
  onConfirm,
  value,
  showWarning
}) => (
  <EditDrawer title="售后自动化任务" showWarning={showWarning}>
    <OrderAftersaleForm
      yz={yz}
      skill={skill}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const OrderTimeoutDrawer = ({
  yz,
  skill,
  agentId,
  onConfirm,
  value,
  showWarning
}) => (
  <EditDrawer title="骑手长时间未接单" showWarning={showWarning}>
    <OrderTimeoutForm
      yz={yz}
      skill={skill}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);

export const OrderPickupStatusDrawer = ({
  yz,
  skill,
  agentId,
  onConfirm,
  value,
  showWarning
}) => (
  <EditDrawer title="骑手长时间未取餐" showWarning={showWarning}>
    <OrderPickupStatusForm
      yz={yz}
      skill={skill}
      agentId={agentId}
      onConfirm={onConfirm}
      value={value}
    />
  </EditDrawer>
);
