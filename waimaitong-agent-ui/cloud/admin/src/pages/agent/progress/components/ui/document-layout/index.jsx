import cx from 'classnames';
import React, { useRef } from 'react';
import {
  Dropdown,
  DropdownClickTrigger,
  DropdownContent,
  DropdownPosition,
  Icon,
  Menu
} from 'zent';
import {
  GoodsDiscardDrawer,
  GoodsOffShelfDrawer,
  GoodsSoldoutDrawer,
  GoodsSoldoutReminderDrawer,
  GoodsUnbindDrawer,
  OrderAftersaleDrawer,
  OrderDiningoutDrawer,
  OrderPickupStatusDrawer,
  OrderTimeoutDrawer,
  ShopSettingDrawer
} from '../../form-field';
// import ShopSelectDialog from "../../form-field/shop-select-dialog";
import { SkillTemplateIdMap } from '../../../constants';
import { validateForm } from '../../../utils';
import './index.scss';

const { MenuItem } = Menu;

const DocumentLayout = ({ title, subtitle, onEdit, sections, yz, formValue, itemSelectorRef }) => {
  // 引用内容区域的DOM元素
  const contentRefs = useRef({});

  // 处理目录项点击事件
  const handleMenuItemClick = id => {
    const element = contentRefs.current[id];
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // 渲染左侧目录
  const renderMenu = () => {
    return (
      <div className="document-menu">
        <div className="document-menu-content">
          {sections.map((section, index) => (
            <div key={section.id} className="menu-section">
              <div className="menu-section-title" onClick={() => handleMenuItemClick(section.id)}>
                {section.title}
              </div>

              {section.subsections?.length > 0 && (
                <div className="menu-subsections">
                  {section.subsections.map((subsection, index) => (
                    <div
                      key={subsection.id}
                      className="menu-subsection-title"
                      onClick={() => handleMenuItemClick(subsection.id)}
                    >
                      {index + 1}. {subsection.title}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const handleConfirm = (skillTemplateId, values) => {
    onEdit(skillTemplateId, values);
  };

  const renderEditItem = skillTemplateId => {
    const skill = {};
    const value = formValue[skillTemplateId];
    const showWarning = !validateForm(skillTemplateId, formValue);
    switch (skillTemplateId) {
      case SkillTemplateIdMap.ShopSetting:
        return (
          <ShopSettingDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.ShopSetting, values)}
            value={value}
          />
        );
      case SkillTemplateIdMap.GoodsSoldout:
        return (
          <GoodsSoldoutDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.GoodsSoldout, values)}
            value={value}
            showWarning={showWarning}
          />
        );
      case SkillTemplateIdMap.GoodsSoldoutReminder:
        return (
          <GoodsSoldoutReminderDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.GoodsSoldoutReminder, values)}
            value={value}
            showWarning={showWarning}
          />
        );
      case SkillTemplateIdMap.GoodsOffShelf:
        return (
          <GoodsOffShelfDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.GoodsOffShelf, values)}
            value={value}
            showWarning={showWarning}
          />
        );
      case SkillTemplateIdMap.GoodsDiscard:
        return (
          <GoodsDiscardDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.GoodsDiscard, values)}
            value={value}
            showWarning={showWarning}
          />
        );
      case SkillTemplateIdMap.GoodsUnbind:
        return (
          <GoodsUnbindDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.GoodsUnbind, values)}
            value={value}
            showWarning={showWarning}
          />
        );
      case SkillTemplateIdMap.OrderDiningout:
        return (
          <OrderDiningoutDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.OrderDiningout, values)}
            value={value}
            itemSelectorRef={itemSelectorRef}
            showWarning={showWarning}
          />
        );
      case SkillTemplateIdMap.OrderAftersale:
        return (
          <OrderAftersaleDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.OrderAftersale, values)}
            value={value}
            showWarning={showWarning}
          />
        );
      case SkillTemplateIdMap.OrderTimeout:
        return (
          <OrderTimeoutDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.OrderTimeout, values)}
            value={value}
            showWarning={showWarning}
          />
        );
      case SkillTemplateIdMap.OrderPickupStatus:
        return (
          <OrderPickupStatusDrawer
            yz={yz}
            skill={skill}
            onConfirm={values => handleConfirm(SkillTemplateIdMap.OrderPickupStatus, values)}
            value={value}
            showWarning={showWarning}
          />
        );
      // case SkillTemplateIdMap.ApplicableStores:
      //   return (
      //     <ShopSelectDialog
      //       value={value}
      //       yz={yz}
      //       onConfirm={(values) =>
      //         handleConfirm(SkillTemplateIdMap.ApplicableStores, values)
      //       }
      //     />
      //   );
      default:
        return null;
    }
  };

  // 渲染右侧内容
  const renderContent = () => {
    return (
      <div className="document-content">
        {sections.map(section => {
          const useableBulletPoints =
            section.bulletPoints?.filter(({ skillTemplateId }) => {
              if (!skillTemplateId) {
                return false;
              }
              const skill = formValue[skillTemplateId];
              return !skill.enable;
            }) ?? [];

          return (
            <div
              key={section.id}
              className="content-section"
              ref={el => (contentRefs.current[section.id] = el)}
            >
              <div
                className={cx('content-section-content', {
                  'content-section-editable': section.editable
                })}
                style={section?.style || {}}
              >
                <h2 className="content-section-title">
                  <p>{section.title}</p>
                  {section.editable && (
                    <p className="editable-icon">{renderEditItem(section.skillTemplateId)}</p>
                  )}
                  {section.addable && useableBulletPoints.length > 0 && (
                    <Dropdown position={DropdownPosition.AutoBottomLeft}>
                      <DropdownClickTrigger>
                        <p className="editable-icon">
                          <Icon type="plus" className="editable-icon-icon" />
                          添加
                        </p>
                      </DropdownClickTrigger>
                      <DropdownContent>
                        <Menu
                          onClick={(_, skillTemplateId) => {
                            handleConfirm(skillTemplateId, {
                              enable: true
                            });
                          }}
                          className="content-section-menu"
                        >
                          {useableBulletPoints.map(({ skillTemplateId, label }) => {
                            return <MenuItem key={skillTemplateId}>{label}</MenuItem>;
                          })}
                        </Menu>
                      </DropdownContent>
                    </Dropdown>
                  )}
                </h2>
                {section.content && (
                  <div className="section-content">
                    {section.content}
                    {section.extraContent}
                  </div>
                )}
                {section.bulletPoints && (
                  <div className="section-content">
                    {section.bulletPoints
                      .filter(({ skillTemplateId }) => {
                        if (!skillTemplateId) {
                          return true;
                        }
                        const skill = formValue[skillTemplateId];
                        return skill.enable;
                      })
                      .map((point, index) => (
                        <div
                          className={cx({
                            'info-item-editable': point.editable
                          })}
                          key={index}
                        >
                          <div className="info-item">
                            <p>
                              {point.label && <strong>{point.label}：</strong>}
                              {point.desc}
                            </p>
                            {point.editable && (
                              <div className="editable-item">
                                {renderEditItem(point.skillTemplateId)}
                                <p
                                  className="editable-item-icon"
                                  onClick={() =>
                                    handleConfirm(point.skillTemplateId, {
                                      enable: false
                                    })
                                  }
                                >
                                  <Icon type="remove-o" className="editable-icon-icon" />
                                  删除
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                )}
                {section.subsections?.map((subsection, index) => (
                  <div
                    key={subsection.id}
                    className="content-subsection"
                    ref={el => (contentRefs.current[subsection.id] = el)}
                  >
                    <div className="content-subsection-item">
                      <h3 className="content-subsection-title">
                        {index + 1}. {subsection.title}
                      </h3>
                      {subsection.editable && (
                        <div className="editable-item">
                          <p className="editable-item-icon">
                            <Icon type="edit-o" className="editable-icon-icon" />
                            修改
                          </p>
                          {/* <p
                                className="editable-item-icon"
                                onClick={() => props.handleDelete(point.key)}
                              >
                                <Icon
                                  type="remove-o"
                                  className="editable-icon-icon"
                                />
                                删除
                              </p> */}
                        </div>
                      )}
                    </div>
                    {/* <h3 className="content-subsection-title">
                      {index + 1}. {subsection.title}
                    </h3> */}

                    {subsection.content && <div className="content-text">{subsection.content}</div>}
                    {subsection.bulletPoints.length && (
                      <div className="section-content">
                        {subsection.bulletPoints.map((point, index) => (
                          <div className="info-item" key={index}>
                            <p>
                              {point.label && <strong>{point.label}：</strong>}
                              {point.desc}
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                    {subsection.bulletPointsExtra && (
                      <div className="point-extra">{subsection.bulletPointsExtra}</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="document-layout">
      <div className="document-container">
        <div className="document-header">
          <h1 className="document-title">{title}</h1>
          <div className="document-subtitle">{subtitle}</div>
        </div>
        <div className="document-body">
          {renderMenu()}
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default DocumentLayout;
