import React from 'react';
import PropTypes from 'prop-types';
import { <PERSON>, Notify, Button } from 'zent';
import Container from '../ui/container';
import AgentBlockWrapper from '../ui/agent-block-wrapper';
import Card from '../ui/card';
import Footer from '../ui/footer';
import {
  TimeoutConfig,
  ShopInfoConfig,
  RecommendInfoConfig,
  RecommendShopInfoList,
  ChannelRunStatus,
  ChannelHelpDescMap,
} from './config';
import './index.scss';
import { TestShopIds } from '../../../constant';
class ShopCheck extends React.Component {
  state = {
    shopInfo: [],
    showShopInfo: false,
    showRecommendInfoBlock: false,
    showRecommendInfo: false,
    showFooter: false,
  };

  timers = [];

  componentDidMount() {
    this.timers = [
      setTimeout(
        () => this.setState({ showShopInfo: true }),
        TimeoutConfig.shopInfo
      ),
      setTimeout(
        () => this.setState({ showRecommendInfoBlock: true }),
        TimeoutConfig.recommendInfoBlock
      ),
      setTimeout(
        () => this.setState({ showRecommendInfo: true }),
        TimeoutConfig.recommendInfo
      ),
      setTimeout(
        () => this.setState({ showFooter: true }),
        TimeoutConfig.footer
      ),
    ];

    this.handleFetch();
  }

  componentWillUnmount() {
    this.timers.forEach(clearTimeout);
  }

  handleFetch = async () => {
    const { agentId } = this.props;
    try {
      const res = await yz.request({
        // url: "/v4/channel/omni/api/omni-channel/queryThirdChannelOverviewInfo.json",
        url: '/v4/third-plugin/waimaitong-agent/queryWmChannelHostRunStatus',
        useBizRequest: true,
        method: 'get',
        data: {
          agentId,
        },
      });
      const shopInfo = (res?.data?.data || []).map((info) => ({
        ...info,
        helpDesc: ChannelHelpDescMap[info.channelId] || '',
      }));

      this.setState({ shopInfo });
    } catch (err) {
      Notify.error(err?.msg || '查询店铺信息失败');
    }
  };

  render() {
    const {
      showShopInfo,
      showRecommendInfoBlock,
      showRecommendInfo,
      shopInfo,
      showFooter,
    } = this.state;
    const { handleNextStep, yz: yzProps } = this.props;
    const { kdtId } = yzProps.data.shop;
    const isTestShop = TestShopIds.includes(kdtId);

    return (
      <div className="check">
        <Container>
          <AgentBlockWrapper>
            <AgentMessageBlock block={ShopInfoConfig} />
            {showShopInfo && shopInfo.length > 0 && (
              <div className="check__cards">
                {shopInfo.map((shop) => (
                  <div
                    className="check__card"
                    key={`channel-${shop.channelId}`}
                  >
                    <Card
                      imgSrc={shop.channelLogo}
                      title={shop.channelName}
                      helpDesc={shop.helpDesc}
                      buttons={
                        <div className="check__status">
                          <div>
                            {shop.runStatus === ChannelRunStatus.UnBind
                              ? '渠道未开通'
                              : '渠道已开通'}
                          </div>
                          <div className="check__status--desc">
                            {shop.runStatus === ChannelRunStatus.UnBind
                              ? '开通后托管生效'
                              : '托管自动生效'}
                          </div>
                        </div>
                      }
                    />
                  </div>
                ))}
              </div>
            )}
          </AgentBlockWrapper>

          {showRecommendInfoBlock && (
            <AgentBlockWrapper showIcon={false}>
              <AgentMessageBlock block={RecommendInfoConfig} />
              {showRecommendInfo && (
                <div className="check__cards">
                  {RecommendShopInfoList.map((shop, index) => (
                    <div className="check__card" key={`recommend-${index}`}>
                      <Card
                        imgSrc={shop.channelLogo}
                        title={shop.channelName}
                        buttons={
                          // <Link href={OmniChannelUrl} target="_blank">
                          //   前往设置
                          // </Link>
                          isTestShop ? null : (
                            <div className="check__desc">即将上线</div>
                          )
                        }
                      />
                    </div>
                  ))}
                </div>
              )}
            </AgentBlockWrapper>
          )}

          {showFooter && (
            <Footer>
              <Button onClick={handleNextStep} type="primary">
                下一步
              </Button>
            </Footer>
          )}
        </Container>
      </div>
    );
  }
}

export default ShopCheck;
