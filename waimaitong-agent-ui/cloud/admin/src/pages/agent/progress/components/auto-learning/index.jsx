import React from 'react';
import { But<PERSON>, BlockLoading } from 'zent';
import AgentBlockWrapper from '../ui/agent-block-wrapper/index.jsx';
import Container from '../ui/container/index.jsx';
import Footer from '../ui/footer/index.jsx';
import { AgentMessageBlockTypeEnum } from '../../constants.js';
import { isEmpty } from '../../utils.js';
import './index.scss';

const CheckIcon = 'https://img01.yzcdn.cn/upload_files/2025/03/20/FnNBo-H55ILM2VXM5e9jCWw0DqzE.png';

const BlockStatus = {
  Doing: 'doing',
  Done: 'done'
};

const CloseThinkingConfigs = [
  {
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始学习本店铺知识',
    isStreamTyping: true,
    status: BlockStatus.Doing,
    doneMarkdown: '已完成本店铺知识的学习'
  },
  {
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始学习店铺信息',
    isStreamTyping: true,
    status: BlockStatus.Doing,
    doneMarkdown: '已学习店铺信息'
  },
  {
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始学习商品信息',
    isStreamTyping: true,
    status: BlockStatus.Doing,
    doneMarkdown: '已学习商品信息'
  },
  {
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始学习外卖渠道信息',
    isStreamTyping: true,
    status: BlockStatus.Doing,
    doneMarkdown: '已学习外卖渠道信息'
  }
];

const CollectInfoConfigs = [
  {
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始搜集和学习行业相关知识，请稍等...',
    isStreamTyping: true,
    status: BlockStatus.Doing,
    doneMarkdown: '已完成行业相关知识的学习'
  },
  {
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始学习行业法律法规',
    isStreamTyping: true,
    status: BlockStatus.Doing,
    doneMarkdown: '已学习行业法律法规'
  },
  {
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始学习外卖平台规则',
    isStreamTyping: true,
    status: BlockStatus.Doing,
    doneMarkdown: '已学习外卖平台规则'
  },
  {
    type: AgentMessageBlockTypeEnum.TEXT,
    markdown: '开始学习外卖行业常见问题',
    isStreamTyping: true,
    status: BlockStatus.Doing,
    doneMarkdown: '已学习外卖行业常见问题'
  }
];

const TimeoutConfig = {
  closeThinking: 12000,
  collectInfo: 22000,
  addItem: 800,
  updateItem: 2000,
  footer: 32000
};

class AutoLearning extends React.Component {
  state = {
    shopInfo: {},
    showCloseThinking: false,
    showCollectInfo: false,
    showFooter: false,
    currentCloseThinkingConfigs: [],
    currentCollectInfoConfigs: []
  };

  timers = new Set();

  componentDidMount() {
    this._isMounted = true;
    this.getShopInfo();
    this.setupProcesses();
  }

  componentWillUnmount() {
    this._isMounted = false;
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
  }

  setupProcesses = () => {
    this.addTimer(() => {
      this.setState({ showCloseThinking: true }, () => {
        this.startProcess(
          CloseThinkingConfigs,
          'currentCloseThinkingConfigs',
          TimeoutConfig.closeThinking
        );
      });
    }, TimeoutConfig.closeThinking);

    this.addTimer(() => {
      this.setState(
        {
          showCollectInfo: true
        },
        () => {
          this.startProcess(
            CollectInfoConfigs,
            'currentCollectInfoConfigs',
            TimeoutConfig.collectInfo
          );
        }
      );
    }, TimeoutConfig.collectInfo);

    this.addTimer(() => this.setState({ showFooter: true }), TimeoutConfig.footer);
  };

  startProcess = (configs, stateKey, baseTime) => {
    // 添加第一个
    this.addTimer(() => {
      this.setState(prev => ({
        [stateKey]: [...prev[stateKey], configs[0]]
      }));
    }, 0);

    const handleNext = () => {
      const startIndex = 1;
      const itemCount = configs.length - startIndex;
      const steps = Array.from({ length: itemCount }, (_, i) => startIndex + i);
      const itemDelay = TimeoutConfig.addItem + TimeoutConfig.updateItem;

      steps.forEach((configIndex, step) => {
        const addDelay = baseTime + step * itemDelay;
        const updateDelay = addDelay + TimeoutConfig.updateItem;

        // 添加
        this.addTimer(() => {
          this.setState(prev => ({
            [stateKey]: [
              ...prev[stateKey],
              {
                ...configs[configIndex],
                status: BlockStatus.Doing
              }
            ]
          }));
        }, addDelay - baseTime);

        // 更新
        if (configIndex !== 0) {
          this.addTimer(() => {
            this.setState(prev => ({
              [stateKey]: prev[stateKey].map((item, i) =>
                i === configIndex
                  ? { ...item, status: BlockStatus.Done, markdown: item.doneMarkdown }
                  : item
              )
            }));
          }, updateDelay - baseTime);
        }
      });

      // 最后更新第一个
      this.addTimer(() => {
        this.setState(prev => ({
          [stateKey]: prev[stateKey].map((item, i) =>
            i === 0 ? { ...item, status: BlockStatus.Done, markdown: item.doneMarkdown } : item
          )
        }));
      }, steps.length * itemDelay);
    };

    this.addTimer(() => {
      handleNext();
    }, TimeoutConfig.addItem);
  };

  addTimer = (fn, delay) => {
    const timer = setTimeout(() => {
      this.timers.delete(timer);
      fn();
    }, delay);
    this.timers.add(timer);
  };

  getShopInfo = () => {
    yz.request({
      url: '/v4/third-plugin/waimaitong-agent/getShopInfo',
      useBizRequest: true,
      method: 'get',
      data: {}
    })
      .then((res = {}) => {
        if (!this._isMounted) return;
        const shopInfo = res?.data?.data || {};
        this.setState({
          shopInfo
        });
      })
      .catch(err => {
        console.error(err?.msg || '查询店铺信息失败');
      });
  };

  render() {
    const {
      showCloseThinking,
      showCollectInfo,
      showFooter,
      currentCloseThinkingConfigs,
      currentCollectInfoConfigs,
      shopInfo
    } = this.state;

    if (isEmpty(shopInfo)) return <div className="loading"><BlockLoading loading /></div>;

    const thoughtChainConfig = {
      type: AgentMessageBlockTypeEnum.THOUGHT_CHAIN,
      isStreamTyping: true,
      thoughtChain: {
        isAutoCollapse: true,
        title: showCloseThinking ? '已完成思考' : '正在深度思考...',
        currentStep: showCloseThinking ? 1 : 0,
        steps: [
          {
            topic: showCloseThinking ? '已完成分析店铺数据' : '正在分析你的店铺数据',
            content: this.generateThoughtContent()
          }
        ]
      }
    };

    return (
      <div className="learning">
        <Container>
          <AgentBlockWrapper>
            <AgentMessageBlock block={thoughtChainConfig} />
            {showCloseThinking &&
            (
              <div className="learning__thinking-block">
                {
                  currentCloseThinkingConfigs.map((config, index) => (
                    <div key={`close-${index}`} className="learning__item">
                      {index > 0 &&
                        (config.status === BlockStatus.Doing ? (
                          <BlockLoading loading className="learning__icon" />
                        ) : (
                          <img className="learning__icon" alt="icon" src={CheckIcon} />
                        ))}
                      <AgentMessageBlock block={config} />
                    </div>
                  ))
                }
              </div>
            )}
          </AgentBlockWrapper>

          {showCollectInfo && (
            <AgentBlockWrapper showIcon={false}>
              {currentCollectInfoConfigs.map((config, index) => (
                <div key={`collect-${index}`} className="learning__item">
                  {index > 0 &&
                    (config.status === BlockStatus.Doing ? (
                      <BlockLoading loading className="learning__icon" />
                    ) : (
                      <img className="learning__icon" alt="icon" src={CheckIcon} />
                    ))}
                  <AgentMessageBlock block={config} />
                </div>
              ))}
            </AgentBlockWrapper>
          )}

          {showFooter && (
            <Footer>
              <Button
                onClick={this.props.handleNextStep}
                type="primary"
              >
                下一步
              </Button>
            </Footer>
          )}
        </Container>
      </div>
    );
  }

  generateThoughtContent = () => {
    const { shopInfo } = this.state;
    return `为了更详细了解你的经营情况、精准推荐合适的外卖渠道，我需要先从店铺信息、商品信息、外卖渠道匹配度评估、外卖行业相关知识进行拆解......  
店铺信息：${
      shopInfo.isRetailChainStore && shopInfo.subShopNum > 0
        ? `共${shopInfo.subShopNum}个分店，`
        : ''
    }主营类目${shopInfo.businessName}${
      shopInfo.province || shopInfo.city ? `，所在地域（${shopInfo.province}${shopInfo.city}` : ''
    }）  
商品类型：在售商品${shopInfo.goodsNum ? `${shopInfo.goodsNum}个` : ''}  
适合开通的外卖渠道：不同外卖平台有不同的用户群体和规则  
外卖行业相关知识：外卖用户基数极为庞大，广泛覆盖各个年龄段。其中，上班族、学生以及年轻家庭用户占比较高。有赞接入的各大主流外卖平台配送范围广泛，基本覆盖城市各个角落，包括偏远的小区、写字楼等。还提供多种配送时段选择，如定时配送，用户可提前规划用餐时间，满足不同场景需求  
通过以上对业务和外卖渠道特点的分析与匹配，为商家客户提供更具针对性的外卖渠道推荐，帮助在竞争激烈的外卖市场中取得更好的经营效果。`;
  };
}

export default AutoLearning;
