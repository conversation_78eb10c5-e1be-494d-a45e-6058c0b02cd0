import { OperatorType, SkillTemplateIdMap } from '../../constants';

export const DefaultValues = {
  [SkillTemplateIdMap.ShopSetting]: {
    enable: true,
  },
  [SkillTemplateIdMap.GoodsSoldout]: {
    enable: true,
    conditions: [
      {
        field: 'stock',
        operator: 'equals',
        value: 0,
      },
    ],
    msgChannels: [],
    receivers: [],
  },
  [SkillTemplateIdMap.GoodsSoldoutReminder]: {
    enable: true,
    conditions: [
      {
        field: 'stock',
        operator: 'equals',
        value: 0,
      },
    ],
  },
  [SkillTemplateIdMap.GoodsDiscard]: {
    enable: true,
    conditions: [
      {
        field: 30,
        operator: 'lessThanOrEqual',
        value: 0,
        props: {
          valueProps: {
            min: 0,
            numerical: true,
          },
        },
      },
    ],
    msgChannels: [],
    receivers: [],
  },
  [SkillTemplateIdMap.GoodsOffShelf]: {
    enable: true,
    conditions: [
      {
        field: 'sellCount',
        operator: 'equals',
        value: 0,
      },
    ],
  },
  [SkillTemplateIdMap.GoodsUnbind]: {
    enable: true,
    notifyType: 1,
    msgChannels: [],
    receivers: [],
  },
  [SkillTemplateIdMap.OrderDiningout]: {
    enable: false,
    instantPeriods: [],
    reservationPeriods: [],
    applicableGoods: { applicableType: 1, itemIds: [] },
  },
  [SkillTemplateIdMap.OrderAftersale]: {
    enable: true,
    conditions: [
      {
        field: 'deliveryStatus',
        operator: OperatorType.Equals,
        value: '未发货',
        props: {
          fieldDisabled: true,
          operatorDisabled: true,
          removeDisabled: true,
          valueProps: {
            disabled: true,
          },
        },
      },
      {
        field: 'orderSource',
        operator: OperatorType.In,
        value: '全部外卖渠道',
        props: {
          fieldDisabled: true,
          operatorDisabled: true,
          removeDisabled: true,
          valueProps: {
            disabled: true,
          },
        },
      },
    ],
  },
  [SkillTemplateIdMap.OrderTimeout]: {
    enable: true,
    conditions: [
      {
        field: 'deliveryTime',
        operator: OperatorType.GreaterThanOrEqual,
        value: 10,
        props: {
          fieldDisabled: true,
          operatorDisabled: true,
          removeDisabled: true,
          valueProps: {
            min: 1,
            max: 60,
            addonAfter: '分钟',
            numerical: true,
          },
        },
      },
    ],
    msgChannels: [],
    receivers: [],
  },
  [SkillTemplateIdMap.OrderPickupStatus]: {
    enable: true,
    conditions: [
      {
        field: 'pickupStatus',
        operator: OperatorType.GreaterThanOrEqual,
        value: 10,
        props: {
          fieldDisabled: true,
          operatorDisabled: true,
          removeDisabled: true,
          valueProps: {
            min: 1,
            max: 60,
            addonAfter: '分钟',
            numerical: true,
          },
        },
      },
    ],
    msgChannels: [],
    receivers: [],
  },
  [SkillTemplateIdMap.ApplicableStores]: {
    applicableType: 1, // 1: 全部店铺, 2: 指定店铺
    storeIds: [],
    applyToNewStores: true,
    channels: [200, 201, 203], // 美团外卖、饿了么外卖、京东秒送
  },
};
