import React, { forwardRef, useEffect } from 'react';
import { FormInputField, FormRadioGroupField, Radio } from 'zent';
import FieldGroup from '../../field-group';
import MsgChannelField from '../base-field/msg-channel-field';
import ReceiversField from '../base-field/receivers-field';
import BaseForm from '../base-form';

const GoodsUnbindForm = forwardRef(
  ({ yz, skill, agentId, onConfirm, value }, ref) => {
    useEffect(() => {
      const { form } = ref.current;
      if (form) {
        form.initialize(value);
      }
    }, []);

    return (
      <BaseForm ref={ref} onSubmit={onConfirm}>
        <FieldGroup title="当系统检测到外卖店铺下非人为直接操作导致已绑定的商品被解绑，默认处理方式：">
          <FormRadioGroupField
            name="notifyType"
            required
            helpDesc="商品绑定关系解除将直接影响商品库存同步"
            withoutLabel
          >
            <Radio value={1}>通知店铺管理员</Radio>
          </FormRadioGroupField>
        </FieldGroup>

        <FieldGroup title="执行操作">
          <MsgChannelField />

          <ReceiversField yz={yz} />

          <FormInputField
            name="notifyContent"
            label="通知文案："
            props={{
              placeholder: '请输入通知文案',
              rows: 4,
              type: 'textarea',
              showCount: true,
              width: 400,
              maxLength: 200,
              disabled: true,
            }}
            defaultValue="[XX]年[XX]月[XX]日[XX]分，[XX]门店的[XX外卖渠道][XX商品名称]监控到异常解绑，请及时介入处理。"
          />
        </FieldGroup>
      </BaseForm>
    );
  }
);

export default GoodsUnbindForm;
