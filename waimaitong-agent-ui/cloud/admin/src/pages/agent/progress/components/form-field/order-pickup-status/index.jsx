import React, { forwardRef, useEffect } from 'react';
import { FormInputField } from 'zent';
import { OperatorText, OperatorType } from '../../../constants';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import MsgChannelField from '../base-field/msg-channel-field';
import ReceiversField from '../base-field/receivers-field';
import BaseForm from '../base-form';

const OrderPickupStatusForm = forwardRef(
  ({ yz, skill, agentId, onConfirm, value }, ref) => {
    useEffect(() => {
      const { form } = ref.current;

      if (form) {
        form.initialize(value);
      }
    }, []);

    return (
      <BaseForm ref={ref} onSubmit={onConfirm}>
        <FieldGroup title="执行条件">
          <ConditionField
            name="conditions"
            conditionOptions={[{ key: 'pickupStatus', text: '骑手取餐时间' }]}
            operatorOptions={[
              {
                key: OperatorType.GreaterThanOrEqual,
                text: OperatorText[OperatorType.GreaterThanOrEqual],
              },
            ]}
            disabled={{
              addon: true,
            }}
          />
        </FieldGroup>

        <FieldGroup title="执行操作">
          <MsgChannelField />

          <ReceiversField yz={yz} />

          <FormInputField
            name="notifyContent"
            label="通知文案："
            props={{
              placeholder: '请输入通知文案',
              rows: 4,
              type: 'textarea',
              showCount: true,
              width: 400,
              maxLength: 200,
              disabled: true,
            }}
            defaultValue="XX年XX月XX日，XX门店的外卖订单【XX有赞订单号】骑手长时间未取餐，请及时介入处理。"
          />
        </FieldGroup>
      </BaseForm>
    );
  }
);

export default OrderPickupStatusForm;
