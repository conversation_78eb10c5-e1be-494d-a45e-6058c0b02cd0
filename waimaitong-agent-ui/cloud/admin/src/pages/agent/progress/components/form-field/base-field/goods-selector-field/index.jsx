import React, { useEffect } from 'react';
import { Form, FormControl, Radio } from 'zent';
import './index.scss';

const RadioGroup = Radio.Group;
const { useField } = Form;

const GoodsSelectorField = ({ itemSelectorRef }) => {
  const applicableGoodsModel = useField('applicableGoods', {});

  useEffect(() => {
    itemSelectorRef.onConfirm((payload) => {
      const { data } = payload;
      applicableGoodsModel.patchValue({
        ...applicableGoodsModel.value,
        itemIds: data.map((item) => item.itemId),
      });
    });

    itemSelectorRef.beforeItemTableRender((tableConfig) => {
      console.log('tableConfig', tableConfig);
      return {
        columns: tableConfig.columns.map((column) => {
          if (column.name === 'sku') {
            return {
              ...column,
              hidden: true,
            };
          }
          return column;
        }),
      };
    });
  }, []);

  useEffect(() => {
    itemSelectorRef.setValue({
      selectedChannelId: [200, 201, 203],
      data: applicableGoodsModel.value?.itemIds?.map((itemId) => ({
        type: 'item',
        itemId,
      })),
    });
  }, [applicableGoodsModel.value]);

  return (
    <FormControl label="适用商品：">
      <RadioGroup
        value={applicableGoodsModel.value.applicableType}
        onChange={(e) => {
          applicableGoodsModel.patchValue({
            applicableType: e.target.value,
            itemIds: [],
          });
        }}
      >
        <div className="goods-selector-field-radio-item">
          <Radio value={1}>全部外卖商品</Radio>
        </div>
        {/* <div className="goods-selector-field-radio-item">
          <Radio value={2}>
            指定指定商品
            <a onClick={() => itemSelectorRef.toggleDialog({ isShow: true })}>
              选择商品
            </a>
          </Radio>
        </div> */}
      </RadioGroup>
    </FormControl>
  );
};

export default GoodsSelectorField;
