import React from "react";
import { But<PERSON>, BlockLoading } from 'zent';
import AgentBlockWrapper from '../ui/agent-block-wrapper/index.jsx';
import Container from '../ui/container/index.jsx';
import Footer from '../ui/footer/index.jsx';
import { AgentMessageBlockTypeEnum } from '../../constants.js';
import { isEmpty } from '../../utils.js';
import "./index.scss";

const CHECK_ICON = 'https://img01.yzcdn.cn/upload_files/2025/03/20/FnNBo-H55ILM2VXM5e9jCWw0DqzE.png';

const CLOSE_THINKING_CONFIGS = [
  { type: AgentMessageBlockTypeEnum.TEXT, markdown: "已完成本店铺知识的学习", isStreamTyping: true },
  { type: AgentMessageBlockTypeEnum.TEXT, markdown: "已学习店铺信息", isStreamTyping: true },
  { type: AgentMessageBlockTypeEnum.TEXT, markdown: "已学习商品信息", isStreamTyping: true },
  { type: AgentMessageBlockTypeEnum.TEXT, markdown: "已学习外卖渠道信息", isStreamTyping: true },
];

const COLLECT_INFO_CONFIGS = [
  { type: AgentMessageBlockTypeEnum.TEXT, markdown: "开始搜集和学习行业相关知识，请稍等...", isStreamTyping: true },
  { type: AgentMessageBlockTypeEnum.TEXT, markdown: "已学习行业法律法规", isStreamTyping: true },
  { type: AgentMessageBlockTypeEnum.TEXT, markdown: "已学习外卖平台规则", isStreamTyping: true },
  { type: AgentMessageBlockTypeEnum.TEXT, markdown: "已学习外卖行业常见问题", isStreamTyping: true },
];

const TIMEOUT_CONFIG = {
  closeThinking: 12000,
  closeThinkingItem: 1500,
  collectInfo: 20000,
  collectInfoItem: 1500,
  footer: 26000
};

class AutoLearning extends React.Component {
  state = {
    shopInfo: {},
    showCloseThinking: false,
    showCollectInfo: false,
    showFooter: false,
    currentCloseThinkingConfigs: [],
    currentCollectInfoConfigs: [],
    closeThinkingIndex: 0,
    collectInfoIndex: 0,
  };

  timers = {
    closeThinking: null,
    collectInfo: null,
    footer: null,
    closeThinkingInterval: null,
    collectInfoInterval: null,
  };

  componentDidMount() {
    this._isMounted = true;
    this.getShopInfo();
    this.setupTimers();
  }

  componentWillUnmount() {
    this._isMounted = false;
    Object.values(this.timers).forEach(timer => {
      if (timer) clearTimeout(timer);
      if (timer) clearInterval(timer);
    });
  }

  setupTimers = () => {
    this.timers.closeThinking = setTimeout(() => {
      this.setState({ showCloseThinking: true }, this.startCloseThinkingInterval);
    }, TIMEOUT_CONFIG.closeThinking);

    this.timers.collectInfo = setTimeout(() => {
      this.setState({ 
        showCollectInfo: true,
        currentCollectInfoConfigs: [COLLECT_INFO_CONFIGS[0]],
        // collectInfoIndex: 1 
      }, this.startCollectInfoInterval);
    }, TIMEOUT_CONFIG.collectInfo);

    this.timers.footer = setTimeout(() => {
      this.setState({ showFooter: true });
    }, TIMEOUT_CONFIG.footer);
  };

  startProgressiveInterval = (configs, stateKey, configsKey, intervalTime) => {
    return setInterval(() => {
      this.setState(prevState => {
        if (prevState[stateKey] < configs.length) {
          return {
            [configsKey]: [...prevState[configsKey], configs[prevState[stateKey]]],
            [stateKey]: prevState[stateKey] + 1
          };
        }
        clearInterval(this.timers[`${stateKey}Interval`]);
        return null;
      });
    }, intervalTime);
  };

  startCloseThinkingInterval = () => {
    this.timers.closeThinkingInterval = this.startProgressiveInterval(
      CLOSE_THINKING_CONFIGS,
      'closeThinkingIndex',
      'currentCloseThinkingConfigs',
      TIMEOUT_CONFIG.closeThinkingItem
    );
  };

  startCollectInfoInterval = () => {
    this.timers.collectInfoInterval = this.startProgressiveInterval(
      COLLECT_INFO_CONFIGS.slice(1),
      'collectInfoIndex',
      'currentCollectInfoConfigs',
      TIMEOUT_CONFIG.collectInfoItem
    );
  };

  getShopInfo = () => {
    yz.request({
      // /v4/shop/setting/shop-info/queryShopInfo.json
      url: '/v4/third-plugin/waimaitong-agent/getShopInfo',
      useBizRequest: true,
      method: 'get',
      data: {},
    })
      .then((res = {}) => {
        if (!this._isMounted) return;
        const { businessName = '', province = '', city = '',subShopNum,isRetailChainStore } = res?.data?.data || {};
        this.setState({ shopInfo: { businessName, province, city, subShopNum, isRetailChainStore } });
      })
      .catch((err) => {
        console.error(err?.msg || "查询店铺信息失败");
      });
  };

  render() {
    const { 
      showCloseThinking,
      showCollectInfo,
      showFooter,
      currentCloseThinkingConfigs,
      currentCollectInfoConfigs,
      shopInfo 
    } = this.state;

    if (isEmpty(shopInfo)) return <BlockLoading loading />;

    const thoughtChainConfig = {
      type: AgentMessageBlockTypeEnum.THOUGHT_CHAIN,
      isStreamTyping: true,
      thoughtChain: {
        isAutoCollapse: true,
        title: showCloseThinking ? "已完成思考" : "正在深度思考...",
        currentStep: showCloseThinking ? 1 : 0,
        steps: [{
          topic: showCloseThinking ? "已完成分析店铺数据" : "正在分析你的店铺数据",
          content: this.generateThoughtContent()
        }]
      }
    };

    if(showFooter){
      currentCollectInfoConfigs[0].markdown = "已完成行业相关知识的学习";
    }

    return (
      <div className="learning">
        <Container>
          <AgentBlockWrapper>
            <AgentMessageBlock block={thoughtChainConfig} />
            {showCloseThinking && currentCloseThinkingConfigs.map((config, index) => (
              <div key={`close-${index}`} className="learning__item">
                {index > 0 && (
                  <img className="learning__icon" alt="icon" src={CHECK_ICON} />
                )}
                <AgentMessageBlock block={config} />
              </div>
            ))}
          </AgentBlockWrapper>

          {showCollectInfo && (
            <AgentBlockWrapper showIcon={false}>
              {currentCollectInfoConfigs.map((config, index) => (
                <div key={`collect-${index}`} className="learning__item">
                  {index > 0 && (
                    <img className="learning__icon" alt="icon" src={CHECK_ICON} />
                  )}
                  <AgentMessageBlock block={config} />
                </div>
              ))}
            </AgentBlockWrapper>
          )}

          {showFooter && (
            <Footer>
              <Button 
                onClick={this.props.handleNextStep} 
                type="primary"
              >
                下一步
              </Button>
            </Footer>
          )}
        </Container>
      </div>
    );
  }

  generateThoughtContent = () => {
    const { shopInfo } = this.state;
    return `
为了更详细了解你的经营情况、精准推荐合适的外卖渠道，我需要先从店铺信息、商品信息、外卖渠道匹配度评估、外卖行业相关知识进行拆解......   
店铺信息：${shopInfo.isRetailChainStore && shopInfo.subShopNum > 0 ?`共${shopInfo.subShopNum}个分店，`:''}主营类目${shopInfo.businessName}${(shopInfo.province ||shopInfo.city)?`，所在地域（${shopInfo.province}${shopInfo.city}`:'' } ）  
适合开通的外卖渠道：不同外卖平台有不同的用户群体和规则  
外卖行业相关知识：外卖用户基数极为庞大，广泛覆盖各个年龄段。其中，上班族、学生以及年轻家庭用户占比较高。有赞接入的各大主流外卖平台配送范围广泛，基本覆盖城市各个角落，包括偏远的小区、写字楼等。还提供多种配送时段选择，如定时配送，用户可提前规划用餐时间，满足不同场景需求  
通过以上对业务和外卖渠道特点的分析与匹配，为商家客户提供更具针对性的外卖渠道推荐，帮助在竞争激烈的外卖市场中取得更好的经营效果。
    `;
  };
}

export default AutoLearning;
