import React, { forwardRef, useImperativeHandle } from 'react';
import { BlockLoading, Form, FormStrategy, ValidateOption } from 'zent';

// 基础表单组件，可被各种具体表单继承
const BaseForm = forwardRef(({ onSubmit, children, loading = false }, ref) => {
  const form = Form.useForm(FormStrategy.View);

  // 通过ref暴露form实例和submit方法给父组件
  useImperativeHandle(ref, () => ({
    form,
    submit: async () => {
      const data = await form.validate(
        ValidateOption.Default |
          ValidateOption.IncludeAsync |
          ValidateOption.IncludeUntouched |
          ValidateOption.IncludeChildrenRecursively
      );
      if (form.isValid()) {
        return await onSubmit?.(form.getValue());
      }
      return Promise.reject();
    },
  }));

  return (
    <BlockLoading loading={loading}>
      <Form layout="horizontal" form={form}>
        {children}
      </Form>
    </BlockLoading>
  );
});

export default BaseForm;
