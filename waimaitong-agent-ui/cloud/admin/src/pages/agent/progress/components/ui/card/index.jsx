import React from 'react';
import './index.scss'; 

class Card extends React.Component {

  static defaultProps = {
    imgSrc: '',
    imageWidth: 48,
    title: '',
  };

  render() {
    const { 
      imgSrc,
      imageWidth,
      title,
      helpDesc='',
      buttons
    } = this.props;

    return (
      <div className="card">
        <div className="card-section">
          <img 
            src={imgSrc}
            alt="channel"
            style={{ width: `${imageWidth}px` }}
            className="card-image"
          />
          <div className="card-text">
            {title && <div className="title">{title}</div>}
            {helpDesc && <div className="help-desc">{helpDesc}</div>}
          </div>
        </div>

          <div className="action-buttons">
            {buttons}
          </div>
      </div>
    );
  }
}

export default Card;
