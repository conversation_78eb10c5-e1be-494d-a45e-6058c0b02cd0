import React, { useRef, useState } from 'react';
import { Button, Drawer, Icon } from 'zent';
import './index.scss';

const EditDrawer = ({ title, children, width = 800, showWarning }) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await formRef.current?.submit?.();
      setVisible(false);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  return (
    <>
      {showWarning && (
        <span onClick={() => setVisible(true)} className="edit-drawer-warning">
          待完善
          <Icon type="right" />
        </span>
      )}
      <span className="editable-item-icon edit-drawer-edit">
        <span onClick={() => setVisible(true)} className="editable-icon">
          <Icon type="edit-o" className="editable-icon-icon" />
          修改
        </span>
      </span>
      <Drawer
        title={title}
        visible={visible}
        onClose={() => setVisible(false)}
        width={width}
        footer={
          <div className="edit-drawer-footer">
            <Button onClick={() => setVisible(false)}>取消</Button>
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              确定
            </Button>
          </div>
        }
        maskClosable
      >
        <div className="edit-drawer-content">
          {React.cloneElement(children, { ref: formRef })}
        </div>
      </Drawer>
    </>
  );
};

export default EditDrawer;
