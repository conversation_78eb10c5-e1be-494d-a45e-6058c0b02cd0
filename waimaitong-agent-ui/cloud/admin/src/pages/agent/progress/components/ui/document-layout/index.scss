/* 文档布局样式 */
// 变量定义
$primary-color: #1677ff;
$text-primary: #333;
$text-secondary: #666;
$text-light: #999;
$bg-white: #fff;
$bg-gray: #f8f8f8;

$font-size-base: 13px;
$font-size-md: 14px;
$font-size-lg: 20px;

$spacing-xs: 4px;
$spacing-sm: 10px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 8px;
$spacing-xxl: 32px;

.document-layout {
  background-color: #fff;
  padding: 16px 8px 16px 16px;
  margin-bottom: 16px;
  border-radius: 8px;
}

.document-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧目录样式 */
.document-menu {
  width: 112px;
  padding: 16px 0;
}

.document-menu-content {
  border-right: 1px solid #eee;
}

.menu-section-title {
  font-size: 12px;
  color: #333;
  cursor: pointer;
  line-height: 17px;
  margin-bottom: 12px;
}

// .menu-section-title:hover {
//   background-color: #f5f5f5;
// }

.menu-subsections {
  padding-left: 16px;
}

.menu-subsection-title {
  font-size: 12px;
  color: #333;
  cursor: pointer;
  line-height: 17px;
  margin-bottom: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-subsection-title:hover {
  background-color: #f5f5f5;
}

/* 右侧内容样式 */
.document-content {
  flex: 1;
  padding: 0 8px;
  overflow-y: auto;
}

.document-header {
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.document-title {
  font-size: 24px;
  font-weight: 500;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 34px;
}

.document-subtitle {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
  margin-bottom: 8px;
}

.info-item {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0px;
  color: #333;
  position: relative;
  display: flex;

  &::before {
    content: "";
    position: absolute;
    left: 4px;
    top: $spacing-sm;
    width: $spacing-xs;
    height: $spacing-xs;
    background-color: $text-primary;
    border-radius: 50%;
  }

  p {
    padding-left: 12px;
  }

  .editable-item {
    display: flex;
    flex: 1;
  }

  &:hover .editable-item-icon {
    visibility: visible;
    // display: block;
    // background-color: #F7F7F7;
  }
  .editable-item-icon {
    color: #666;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    visibility: hidden;
    // position: relative;
    // top: 4px;
    // right: 8px;
  }
}

.info-item-editable :hover {
  background: #f7f7f7;
}

.content-section {
  border-bottom: 1px solid #eee;
  padding: 8px 0;
  border-radius: 8px;

  &:last-child {
    border-bottom: none;
  }
}

.content-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  line-height: 22px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-section-content {
  padding: 8px;
  border-radius: 8px;
  .editable-icon {
    color: #666;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    // position: relative;
    // top: 4px;
    // right: 8px;
    // display: none;
  }
  .editable-icon-icon {
    font-size: 20px;
  }
}

.content-section-editable:hover {
  background-color: #f7f7f7;
}

.content-section-menu {
  width: 160px !important;
}

.section-content {
  line-height: 24px;
}

.content-subsection {
  margin-bottom: 16px;
  &:hover {
    background-color: #f7f7f7;
    .editable-item-icon {
      visibility: visible;
    }
    // display: block;
    // background-color: #F7F7F7;
  }
}

.content-subsection-item {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;

  .editable-item {
    display: flex;
    gap: 12px;
  }

  .editable-item-icon {
    color: #666;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    visibility: hidden;
    // position: relative;
    // top: 4px;
    // right: 8px;
  }
}

.content-subsection-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  //   margin-top: 12px;
  line-height: 28px;
}

.content-text {
  font-family: PingFang SC;
  font-size: 14px;
  // line-height: 100%;
  letter-spacing: 0px;
  color: #333;
}

.content-list {
  padding-left: 20px;
  margin: 8px 0;
}

.content-list-item {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
  position: relative;
  list-style-type: none;
}

.content-list-item::before {
  content: "•";
  position: absolute;
  left: -12px;
  color: #666;
}

/* 调整目录和内容的对应关系 */
.content-section:nth-child(1) .content-section-title {
  margin-top: 0;
}

/* 确保滚动平滑 */
html {
  scroll-behavior: smooth;
}
