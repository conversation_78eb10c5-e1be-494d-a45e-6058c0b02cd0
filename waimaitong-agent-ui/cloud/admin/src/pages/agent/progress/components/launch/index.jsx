import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Notify } from 'zent';
import { AgentMessageBlockTypeEnum, AgentTaskExecutionStatusEnum } from '../../constants';
import AgentBlockWrapper from '../ui/agent-block-wrapper';
import Container from '../ui/container';
import Footer from '../ui/footer';
import Card from '../ui/card';
import Divider from '../ui/divider';
import {
  transformGoodsSkill,
  transformOrderSkill,
  transformShopSkill
} from './utils/transform-skill';
import { OmniChannelUrl, ChannelRunStatus } from '../shop-check/config';
import { Stage } from '../../../constant';
import './index.scss';

const startLaunchBlock = {
  type: AgentMessageBlockTypeEnum.TEXT,
  markdown: `开始执行方案：`,
  isStreamTyping: true
};

const launchBlockTemplate = {
  type: AgentMessageBlockTypeEnum.TASK_EXECUTION,
  taskExecution: {
    steps: [
      {
        status: AgentTaskExecutionStatusEnum.PENDING,
        desc: '配置店铺设置托管',
        executionContent: '正在配置店铺设置托管',
        completedContent: '已配置店铺设置托管',
        failedContent: '配置店铺设置托管失败'
      },
      {
        status: AgentTaskExecutionStatusEnum.PENDING,
        desc: '配置商品托管',
        executionContent: '正在配置商品托管',
        completedContent: '已配置商品托管',
        failedContent: '配置商品托管失败'
      },
      {
        status: AgentTaskExecutionStatusEnum.PENDING,
        desc: '配置订单托管',
        executionContent: '正在配置订单托管',
        completedContent: '已配置订单托管',
        failedContent: '配置订单托管失败'
      },
      {
        status: AgentTaskExecutionStatusEnum.PENDING,
        desc: '配置使用范围',
        executionContent: '正在配置使用范围',
        completedContent: '已配置使用范围',
        failedContent: '配置使用范围失败'
      }
    ],
    currentStep: -1
  }
};

const RecommendInfoConfig = {
  type: AgentMessageBlockTypeEnum.TEXT,
  markdown: `你有以下渠道未开通或绑定店铺，可通过前往设置进行开通或绑定。`,
  isStreamTyping: true
};

class Launch extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showLaunchBlock: false,
      showRecommendInfoBlock: false,
      // showRecommendInfo: false,
      showFooter: false,
      steps: launchBlockTemplate.taskExecution.steps.map((step, index) => {
        const plan = props.planList[index];
        return {
          ...step,
          desc: plan || step.desc,
          completedContent: plan ? `已${plan}` : step.completedContent
        };
      }),
      currentStep: -1,
      shopInfo: []
    };
    this.timeouts = [];
  }

  componentDidMount() {
    this.fetchChannelList();

    this.setupTimers();
  }

  componentWillUnmount() {
    this.timeouts.forEach(clearTimeout);
  }

  setupTimers = () => {
    this.timeouts.push(
      setTimeout(() => {
        this.setState({ showLaunchBlock: true });
      }, 1000),
      setTimeout(() => {
        this.updateStepStatus(0, AgentTaskExecutionStatusEnum.RUNNING);
        this.handleInitSkill();
      }, 3000)
    );
  };

  async handleInitSkill() {
    const { formValue, agentId } = this.props;
    const skillRes = await yz.request({
      url: '/v4/third-plugin/waimaitong-agent/querySkills',
      useBizRequest: true,
      method: 'get',
      data: {
        agentId
      }
    });
    const skillList = skillRes.data.data;

    try {
      await this.executeStep(0, async () => {
        await yz.request({
          url: '/v4/third-plugin/waimaitong-agent/updateWmShopHostConfig',
          useBizRequest: true,
          method: 'post',
          data: transformShopSkill({ formValue }),
          header: { 'Content-Type': 'application/json' }
        });
      });

      await this.executeStep(1, async () => {
        await yz.request({
          url: '/v4/third-plugin/waimaitong-agent/saveHostAgentSkill',
          useBizRequest: true,
          method: 'post',
          data: transformGoodsSkill({
            formValue,
            agentId,
            skillList
          }),
          header: { 'Content-Type': 'application/json' }
        });
      });

      await this.executeStep(2, async () => {
        await yz.request({
          url: '/v4/third-plugin/waimaitong-agent/createOrderSkill',
          useBizRequest: true,
          method: 'post',
          data: transformOrderSkill({
            formValue,
            agentId,
            skillList
          }),
          header: { 'Content-Type': 'application/json' }
        });
      });

      await this.executeStep(3, async () => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        this.setState({
          showRecommendInfoBlock: true
        });
        setTimeout(() => {
          this.setState({ showFooter: true });
        }, 1500);
        startLaunchBlock.markdown = '方案执行完成，外卖托管已成功开启。';
      });
    } catch (error) {
      // Notify.error(error?.msg || '流程执行异常');
    }
  }

  executeStep = async (stepIndex, task) => {
    try {
      await task();
      this.completeStep(stepIndex);
    } catch (error) {
      this.failStep(stepIndex);
      throw error;
    }
  };

  updateStepStatus = (stepIndex, newStatus) => {
    this.setState(prevState => {
      const newSteps = [...prevState.steps];
      newSteps[stepIndex] = { ...newSteps[stepIndex], status: newStatus };
      return { steps: newSteps, currentStep: prevState.currentStep + 1 };
    });
  };

  completeStep = stepIndex => {
    this.setState(prevState => {
      const newSteps = [...prevState.steps];
      newSteps[stepIndex] = {
        ...newSteps[stepIndex],
        status: AgentTaskExecutionStatusEnum.COMPLETED
      };

      const nextStep = stepIndex + 1;
      if (nextStep < newSteps.length) {
        newSteps[nextStep] = {
          ...newSteps[nextStep],
          status: AgentTaskExecutionStatusEnum.RUNNING
        };
      }

      return {
        steps: newSteps,
        currentStep: nextStep
      };
    });
  };

  failStep = stepIndex => {
    this.setState(prevState => {
      const newSteps = [...prevState.steps];
      newSteps[stepIndex] = {
        ...newSteps[stepIndex],
        status: AgentTaskExecutionStatusEnum.FAILED
      };
      return { steps: newSteps };
    });
  };

  fetchChannelList = async () => {
    const { agentId } = this.props;
    try {
      const res = await yz.request({
        // url: '/v4/channel/omni/api/omni-channel/queryThirdChannelOverviewInfo.json',
        url: '/v4/third-plugin/waimaitong-agent/queryWmChannelHostRunStatus',
        useBizRequest: true,
        method: 'get',
        data: {
          agentId
        }
      });

      const shopInfo = (res?.data?.data || []).filter(
        info => info.runStatus === ChannelRunStatus.UnBind
      );

      this.setState({ shopInfo });
    } catch (err) {
      Notify.error(err?.msg || '查询店铺信息失败');
    }
  };

  getLaunchBlock = () => {
    const { currentStep, steps } = this.state;
    return {
      ...launchBlockTemplate,
      taskExecution: {
        ...launchBlockTemplate.taskExecution,
        steps,
        currentStep
      }
    };
  };

  handleNextClick = () => {
    this.props.handleChangeStage(Stage.List);
  };

  render() {
    const { showLaunchBlock, showFooter, showRecommendInfoBlock, shopInfo } = this.state;
    const dynamicLaunchBlock = this.getLaunchBlock();

    return (
      <div className="launch">
        <Container>
          <AgentBlockWrapper>
            <AgentMessageBlock block={startLaunchBlock} />
            {showLaunchBlock && (
              <div className="launch__steps">
                <AgentMessageBlock block={dynamicLaunchBlock} />
              </div>
            )}
          </AgentBlockWrapper>
          {showRecommendInfoBlock && (
            <AgentBlockWrapper showIcon={false}>
              <AgentMessageBlock block={RecommendInfoConfig} />
              {showFooter && (
                <div className="launch__cards">
                  {shopInfo.map((shop, index) => (
                    <div className="launch__card" key={`recommend-${index}`}>
                      <Card
                        imgSrc={shop.channelLogo}
                        title={shop.channelName}
                        buttons={
                          <Divider>
                            <Link href={OmniChannelUrl} target="_blank">
                              前往设置
                            </Link>
                            <Link onClick={this.fetchChannelList}>刷新</Link>
                          </Divider>
                        }
                      />
                    </div>
                  ))}
                </div>
              )}
            </AgentBlockWrapper>
          )}
          {showFooter && (
            <Footer>
              <Button onClick={this.handleNextClick} type="primary">
                完成
              </Button>
            </Footer>
          )}
        </Container>
      </div>
    );
  }
}

export default Launch;
