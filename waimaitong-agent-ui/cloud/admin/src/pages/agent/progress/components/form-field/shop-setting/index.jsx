import React, { forwardRef, useEffect } from 'react';
import {
  Checkbox,
  FormCheckboxGroupField,
  FormRadioGroupField,
  Radio,
} from 'zent';
import FieldGroup from '../../field-group';
import BaseForm from '../base-form';
import { SyncFieldMap } from './constants';
import './index.scss';

const ShopSettingForm = forwardRef(({ yz, onConfirm, value }, ref) => {
  useEffect(() => {
    const form = ref.current?.form;

    if (form) {
      // 初始化表单
      form.initialize(value);
    }
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={onConfirm}>
      <FieldGroup title="店铺">
        <FormRadioGroupField
          name="operateStatus"
          label="店铺经营状态："
          required
          helpDesc="设置外部渠道的店铺经营状态，保存后系统会自动更改营业状态。"
        >
          <Radio value={1}>营业</Radio>
          <Radio value={0}>休息</Radio>
        </FormRadioGroupField>

        <FormRadioGroupField
          name="syncBusinessHours"
          label="营业时间同步："
          required
          helpDesc={
            <>
              将有赞店铺已有营业时间同步至外部店铺
              {value.shopRole && (
                <>
                  ，可在"
                  <a
                    onClick={() => {
                      yz.navigateTo({
                        route:
                          'https://store.youzan.com/v2/retailshop/org-manage',
                        blank: true,
                      });
                    }}
                  >
                    店铺-组织机构
                  </a>
                  "中设置有赞店铺营业时间。
                </>
              )}
              <br />
              支持每天重复的营业时间同步，每周重复设置暂不支持。
            </>
          }
        >
          <Radio value={1}>同步</Radio>
          <Radio value={0}>不同步</Radio>
        </FormRadioGroupField>
      </FieldGroup>

      <FieldGroup title="库存同步规则">
        <FormRadioGroupField
          name="stockThreshold"
          label="同步时效要求："
          required
          helpDesc="库存敏感度高（如面包/蛋糕/商超等商家）建议超高要求，日库存充足（如餐饮/咖啡茶饮等商家）选择中等要求"
        >
          <Radio value={50}>超高要求</Radio>
          <Radio value={15}>中等要求</Radio>
        </FormRadioGroupField>
      </FieldGroup>

      <FieldGroup title="同步外卖商品字段">
        {SyncFieldMap.map((field) => (
          <FormCheckboxGroupField
            key={field.group}
            name={field.group}
            label={`${field.groupLabel}：`}
            className="shop-setting-checkbox-group-field"
          >
            {field.fields.map((field) => (
              <Checkbox
                style={{ visibility: field.hidden ? 'hidden' : 'visible' }}
                key={field.name}
                value={field.name}
              >
                {field.label}
              </Checkbox>
            ))}
          </FormCheckboxGroupField>
        ))}
      </FieldGroup>

      <FieldGroup title="外卖开票设置">
        <FormRadioGroupField
          name="allowOpenInvoice"
          label="扫小票二维码开票："
          required
          helpDesc={`开启后，可通过扫外卖订单小票"开票二维码"开具发票。请务必前里准定有效商品和外卖渠道通商品，否则无法开票。`}
        >
          <Radio value={true}>开启</Radio>
          <Radio value={false}>关闭</Radio>
        </FormRadioGroupField>
      </FieldGroup>
    </BaseForm>
  );
});

export default ShopSettingForm;
