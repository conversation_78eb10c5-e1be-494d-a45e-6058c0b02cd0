import React, { forwardRef, useEffect } from 'react';
import { FormInputField } from 'zent';
import FieldGroup from '../../field-group';
import ConditionField from '../base-field/condition-field';
import MsgChannelField from '../base-field/msg-channel-field';
import ReceiversField from '../base-field/receivers-field';
import BaseForm from '../base-form';

const GoodsSoldoutForm = forwardRef(({ yz, onConfirm, value }, ref) => {
  useEffect(() => {
    const { form } = ref.current;
    if (form) {
      form.initialize(value);
    }
  }, []);

  return (
    <BaseForm ref={ref} onSubmit={onConfirm}>
      <FieldGroup title="执行条件">
        <ConditionField
          name="conditions"
          disabled
          operatorOptions={[{ key: 'equals', text: '=' }]}
          conditionOptions={[{ key: 'stock', text: '库存数量' }]}
        />
      </FieldGroup>

      <FieldGroup title="执行操作">
        <MsgChannelField />

        <ReceiversField yz={yz} />

        <FormInputField
          name="notifyContent"
          label="通知文案："
          props={{
            placeholder: '请输入通知文案',
            rows: 4,
            type: 'textarea',
            showCount: true,
            width: 400,
            maxLength: 200,
            disabled: true,
          }}
          defaultValue="[XX]年[XX]月[XX]日[XX]分，[XX]门店的[XX外卖渠道][XX商品名称]已售罄，请知悉。"
        />
      </FieldGroup>
    </BaseForm>
  );
});

export default GoodsSoldoutForm;
