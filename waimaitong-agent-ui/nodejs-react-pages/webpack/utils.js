'use strict';
const path = require('path');
const glob = require('fast-glob');
const {appName} = require('../../config');
const ENTRY_PATH = path.resolve(__dirname, '../src');

exports.entries = function () {

  let entryFiles = glob.sync(['pages/**/main.js'], {
    cwd: ENTRY_PATH
  });
  const map = {};
  entryFiles.forEach((filePath) => {
    let key = filePath.replace('/main.js', '');
    map[key] = path.resolve(ENTRY_PATH, filePath);
  });
  return map;
};

exports.getAppName = function () {
  return appName;
};
exports.getNodejsConfigDir = function () {
  return path.resolve(__dirname, '../../../config');
};
exports.devOutputDir = function () {
  return path.resolve(__dirname, '../../../app/public/react');
};
exports.prodOutputIdr = function () {
  return path.resolve(__dirname, '../dist');
};

