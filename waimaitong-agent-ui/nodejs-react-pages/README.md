# 电商云 - 前端页面开发脚手架（vue）

> 有赞定制vue前端页面

## 目录结构说明：

```bash
.
├── src                      代码目录
│   └── pages                   客户端页面入口
├── node_modules                node_modules
└── webpack                     打包配置文件目录
```
## 注意事项（必读）
- webpack配置多入口打包方案，会自动扫描src/pages下所有的main.js文件，作为打包入口文件进行打包

## Build Setup

``` bash
# 安装依赖
npm install

# 本地编译
```
npm run dev
```
会将js、css编译到 app/public/vue目录下， 并且实时打包

# 发布
```
npm run build
```
将js、css编译上传到cdn，并将页面名和js、css的cdn地址写入config/vue.js.json、config/vue.css.json文件中
