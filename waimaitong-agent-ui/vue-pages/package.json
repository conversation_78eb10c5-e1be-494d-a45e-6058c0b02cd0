{"name": "vue-pages", "version": "0.1.0", "description": "有赞定制vue前端页面", "author": "yinxiongfei <<EMAIL>>", "private": true, "scripts": {"dev": "node webpack/dev.js", "build": "node webpack/build.js"}, "dependencies": {"vant": "^2.2.0", "vue": "^2.5.2", "vue-router": "^3.0.1", "zan-ajax": "^2.0.0"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-import": "^1.11.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "concurrently": "^4.1.0", "css-loader": "^0.28.0", "eslint": "^4.15.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.0.0", "express": "^4.17.1", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "fs-extra": "^8.1.0", "glob": "^7.1.3", "html-webpack-plugin": "^2.30.1", "http-proxy-middleware": "^0.19.1", "node-notifier": "^5.1.2", "node-sass": "^4.12.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "precss": "^4.0.0", "raw-loader": "^3.0.0", "rimraf": "^2.6.0", "sass-loader": "7.1.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "style-loader": "0.23.0", "stylus-loader": "^3.0.2", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0", "youzanyun": "^0.1.13"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "homepage": "/vue/"}