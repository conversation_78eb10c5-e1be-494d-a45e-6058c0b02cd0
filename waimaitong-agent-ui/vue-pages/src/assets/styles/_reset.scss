* {
  -webkit-tap-highlight-color: transparent;
}

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  font-size: 100%;
  vertical-align: baseline
}

html {
  line-height: 1
}

ol,
ul {
  list-style: none
}

table {
  border-collapse: collapse;
  border-spacing: 0
}

caption,
th,
td {
  font-weight: normal;
  vertical-align: middle
}

a img {
  border: none
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary {
  display: block
}

html {
  -webkit-text-size-adjust: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Roboto, 'Arial', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft Yahei', SimSun, sans-serif;
}

body {
  color: #333;
  background-color: #f8f8f8;
  -webkit-backface-visibility: hidden;
  -webkit-text-size-adjust: 100% !important;
  -webkit-font-smoothing: antialiased;
}

strong {
  font-weight: bold;
}

a {
  color: #333;
  background: transparent;
  text-decoration: none;
}

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

h2 {
  font-size: 18px;
  line-height: 22px;
}

h3 {
  font-size: 15px;
  line-height: 18px;
}

button,
[type='number'],
[type='text'],
[type='password'],
[type='email'],
[type='search'],
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  margin: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* 处理ios9以上非点击元素事件委托监听失效 */
div,
span,
li,
dt,
dd {
  cursor: pointer;
}
