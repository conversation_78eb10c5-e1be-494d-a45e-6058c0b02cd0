<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <include resource="com/youzan/boot/logging/logback/youzan-base.xml" />
    <include resource="com/youzan/boot/logging/logback/youzan-dubbo.xml" />
    <include resource="com/youzan/boot/logging/logback/youzan-sql.xml" />
    <springProperty scope="context" name="springProfilesActive"
                    source="spring.profiles.active" defaultValue="dev"/>

    <if condition='!property("springProfilesActive").contains("dev")'>
        <then>
            <include resource="com/youzan/boot/logging/logback/youzan-track.xml" />

            <springProperty scope="context" name="pushmq_consumer_topic"
                            source="pushmq.result.topic"
                            defaultValue="pushmq_consume_result"/>
            <!--上传日志到日志平台-->
            <appender name="RESULT" class="com.youzan.track.appender.TrackAppender">
                <!-- 应用名称，必须和cmdb中注册的应用名保持一致 -->
                <app>cloud-open-message</app>
                <!-- 上报的日志主题名称，必须和在日志中心中定义的logindex名称保持一致 -->
                <topic>${pushmq_consumer_topic}</topic>
                <pattern>${TRACK_PATTERN}</pattern>
                <!--测试环境指向**************，线上环境指向127.0.0.1 -->
                <host>${TRACK_HOST}</host>
            </appender>

            <logger name="consumer_result" level="INFO">
                <appender-ref ref="RESULT" />
            </logger>
        </then>

    </if>

    <root level="${ROOT_LOGGER_LEVEL}">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="ROOT" />
        <appender-ref ref="ERROR" />
        <appender-ref ref="ASYNC" />
        <if condition='!property("springProfilesActive").contains("dev")'>
            <then>
                <appender-ref ref="TRACK"/>
            </then>
        </if>
    </root>

</configuration>
