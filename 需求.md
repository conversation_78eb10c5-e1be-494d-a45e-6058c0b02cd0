## 店铺托管设置需求变更

### 需求列表

#### 库存同步规则模块删除

不再展示库存同步规则配置

#### 店铺配置下方新增“商品同步规则”
商品同步规则等同于“同步外卖商品字段”模块，但是比“同步外卖商品字段”模块更详细，更复杂。具体字段如下：

1. 更新范围：单选，无默认值。候选值如下：
```typescript
export enum EItemSyncRange {
  all = '0',           // 全部信息
  partial = '1',       // 指定部分信息
  bindOnly = '2',      // 仅绑定商品，不更新信息
}
```
显示顺序：指定部分信息、全部信息、仅绑定商品，不更新信息

2. 销售信息：多选，无默认值，同目前的“库存数量”字段， 候选值如下：
- takeUpOrDown: 销售状态（上下架）
- 库存: stockNum

3. 基础信息：多选，无默认值，候选值如下：
- title: 商品名称
- skuName: 规格名称
- barcode: 商品/规格条码
- category: 商品类目
- prop: 属性
- picture: 商品主图
- group: 商品分组
- sellPoint: 商品描述
- minimumPurchaseNum: 最小起购量

4. 价格信息：多选，无默认值，候选值如下：
- price: 价格
- packingCharge: 商品打包费

5. 库存同步规则：单选，默认值15。demo如下：
```
<FormRadioGroupField
    name={'stockRealTimeSync'}
    label="库存同步规则："
    required
    props={{ disabled }}
    defaultValue="15"
  >
    <Radio value="15">
      当商品增加库存，或可售库存≤15个时，实时同步
      <div className="tip">
        适合日库存固定/有限商家（如面包/商超便利等业态）
      </div>
    </Radio>
    <Radio value="50">
      当商品增加库存，或可售库存≤50个时，实时同步
      <div className="tip">
        适合日库存充足/无限商家（如正餐西餐、咖啡茶饮、仅生日蛋糕等业态）
      </div>
    </Radio>
  </FormRadioGroupField>
```
6. 商品库发布外卖商品时: 单选，无默认值。候选值如下：
```typescript
export enum EAutoPublishSync {
  auto = '1',          // 自动发布创建到外卖平台
  manual = '0',        // 手动同步创建到外卖平台
}

```

7. 总部可修改分店外卖商品：多选，无默认值，候选值如下：

- packingCharge: 商品打包费
- minimumPurchaseNum: 最小起购量
- title: 商品名称
- group: 商品分组
- price: 价格
- skuDisableStatus: 规格启/禁用

表单组件的helpDesc展示内容: 默认总部强管控,商品库更新将覆盖分店外卖渠道商品信息非总部强管控可取消勾选。
